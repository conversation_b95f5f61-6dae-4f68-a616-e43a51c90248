/**
 * 认证调试工具
 * 用于检查和调试认证相关问题
 */

export const AuthDebug = {
  /**
   * 检查 JWT token 状态
   */
  checkToken() {
    const token = localStorage.getItem('jwt');
    console.group('🔐 JWT Token 状态检查');
    
    if (!token) {
      console.error('❌ 未找到 JWT token');
      console.log('💡 请先登录获取 token');
      console.groupEnd();
      return false;
    }
    
    console.log('✅ JWT token 存在');
    console.log('📝 Token 长度:', token.length);
    console.log('🔍 Token 前缀:', token.substring(0, 20) + '...');
    
    // 尝试解析 JWT payload (不验证签名)
    try {
      const parts = token.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(atob(parts[1]));
        console.log('📋 Token payload:', payload);
        
        // 检查过期时间
        if (payload.exp) {
          const expDate = new Date(payload.exp * 1000);
          const now = new Date();
          const isExpired = expDate < now;
          
          console.log('⏰ Token 过期时间:', expDate.toLocaleString());
          console.log('🕐 当前时间:', now.toLocaleString());
          console.log(isExpired ? '❌ Token 已过期' : '✅ Token 未过期');
          
          if (isExpired) {
            console.log('💡 请重新登录获取新的 token');
            console.groupEnd();
            return false;
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ 无法解析 token payload:', error);
    }
    
    console.groupEnd();
    return true;
  },

  /**
   * 测试 GraphQL 连接
   */
  async testGraphQLConnection() {
    console.group('🌐 GraphQL 连接测试');
    
    try {
      const response = await fetch('/server/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: '{ __schema { types { name } } }'
        })
      });
      
      if (response.ok) {
        console.log('✅ GraphQL 服务连接正常');
        const data = await response.json();
        console.log('📊 可用类型数量:', data.data?.__schema?.types?.length || 0);
      } else {
        console.error('❌ GraphQL 服务连接失败:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('❌ GraphQL 连接错误:', error);
    }
    
    console.groupEnd();
  },

  /**
   * 测试认证的 GraphQL 请求
   */
  async testAuthenticatedRequest() {
    console.group('🔒 认证请求测试');

    const token = localStorage.getItem('jwt');
    if (!token) {
      console.error('❌ 无法测试：未找到 JWT token');
      console.groupEnd();
      return;
    }

    console.log('🔑 使用的 Token:', token.substring(0, 50) + '...');

    try {
      // 测试通过代理的请求
      console.log('📡 测试代理请求 (/server/api/graphql)...');
      const proxyResponse = await fetch('/server/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          query: `{
            uaAuthorityInfos(needPage: true, pageSize: 1, pageNum: 1) {
              ok
              code
              message
            }
          }`
        })
      });

      const proxyData = await proxyResponse.json();
      console.log('📄 代理响应状态:', proxyResponse.status);
      console.log('📋 代理响应数据:', proxyData);

      // 测试直接请求后端
      console.log('🎯 测试直接后端请求 (localhost:8881)...');
      const directResponse = await fetch('http://localhost:8881/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          query: `{
            uaAuthorityInfos(needPage: true, pageSize: 1, pageNum: 1) {
              ok
              code
              message
            }
          }`
        })
      });

      const directData = await directResponse.json();
      console.log('📄 直接响应状态:', directResponse.status);
      console.log('📋 直接响应数据:', directData);

      // 比较结果
      if (proxyResponse.status !== directResponse.status) {
        console.warn('⚠️ 代理和直接请求的状态码不同！');
        console.log('代理状态:', proxyResponse.status);
        console.log('直接状态:', directResponse.status);
      }

      if (proxyResponse.ok && !proxyData.errors) {
        console.log('✅ 认证请求成功');
      } else {
        console.error('❌ 认证请求失败');

        if (proxyData.errors?.[0]?.message === '无permissions') {
          console.log('💡 建议：检查用户权限或重新登录');
        }
      }
    } catch (error) {
      console.error('❌ 认证请求错误:', error);
    }

    console.groupEnd();
  },

  /**
   * 检查环境配置
   */
  checkEnvironment() {
    console.group('🌍 环境配置检查');

    console.log('📋 环境变量:');
    console.log('  NODE_ENV:', process.env.NODE_ENV);
    console.log('  REACT_APP_ENV:', process.env.REACT_APP_ENV);
    console.log('  UMI_ENV:', process.env.UMI_ENV);

    const currentEnv = process.env.REACT_APP_ENV || 'local';
    console.log('🎯 当前使用的代理环境:', currentEnv);

    // 显示当前代理配置
    const proxyConfigs = {
      local: {
        target: 'http://localhost:8881/',
        pathRewrite: { '^/server/api': '' }
      },
      dev: {
        target: 'http://localhost:8082/',
        pathRewrite: { '^/server': '' }
      }
    };

    const currentProxy = proxyConfigs[currentEnv as keyof typeof proxyConfigs];
    if (currentProxy) {
      console.log('🔗 当前代理配置:');
      console.log('  目标服务器:', currentProxy.target);
      console.log('  路径重写:', currentProxy.pathRewrite);
      console.log('  完整URL: /server/api/graphql →', currentProxy.target + 'graphql');
    } else {
      console.warn('⚠️ 未找到当前环境的代理配置');
    }

    console.groupEnd();
  },

  /**
   * 完整的认证状态检查
   */
  async fullCheck() {
    console.log('🚀 开始完整的认证状态检查...');

    this.checkEnvironment();
    this.checkToken();
    await this.testGraphQLConnection();
    await this.checkCurrentUser();
    await this.testAuthenticatedRequest();

    console.log('✨ 认证状态检查完成');
  },

  /**
   * 检查当前用户信息
   */
  async checkCurrentUser() {
    console.group('👤 当前用户信息检查');

    const token = localStorage.getItem('jwt');
    if (!token) {
      console.error('❌ 无法检查：未找到 JWT token');
      console.groupEnd();
      return;
    }

    try {
      const response = await fetch('/server/api/auth/currentUser', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('✅ 当前用户信息:', userData);

        if (userData.data) {
          console.log('👤 用户名:', userData.data.userInfo?.name);
          console.log('🎭 角色:', userData.data.roles);
          console.log('🔐 权限:', userData.data.permissions);

          // 检查是否有必要的权限
          const requiredPermissions = ['ua-authority-info', 'ua-role-info', 'ua-user-info'];
          const userPermissions = userData.data.permissions || [];

          console.log('🔍 权限检查:');
          requiredPermissions.forEach(perm => {
            const hasPermission = userPermissions.some((userPerm: string) =>
              userPerm.toLowerCase() === perm.toLowerCase()
            );
            console.log(`  ${hasPermission ? '✅' : '❌'} ${perm}: ${hasPermission ? '有权限' : '无权限'}`);
          });
        }
      } else {
        console.error('❌ 获取用户信息失败:', response.status, response.statusText);
        const errorData = await response.text();
        console.error('错误详情:', errorData);
      }
    } catch (error) {
      console.error('❌ 检查用户信息时出错:', error);
    }

    console.groupEnd();
  },

  /**
   * 清除认证信息
   */
  clearAuth() {
    localStorage.removeItem('jwt');
    console.log('🧹 已清除 JWT token，请重新登录');
    console.log('🔗 登录地址: http://localhost:5081/user/login');
  },

  /**
   * 快速修复 JWT 签名问题
   */
  async fixJwtSignatureIssue() {
    console.group('🔧 修复 JWT 签名问题');

    console.log('📋 问题诊断：JWT 签名验证失败');
    console.log('💡 原因：后端服务重启导致签名密钥变化');
    console.log('🛠️ 解决方案：清除旧 token 并重新登录');

    // 清除旧 token
    this.clearAuth();

    // 提供登录链接
    console.log('');
    console.log('📝 请按以下步骤操作：');
    console.log('1. 点击登录链接: http://localhost:5081/user/login');
    console.log('2. 输入用户名和密码重新登录');
    console.log('3. 登录成功后，新的 JWT token 会自动保存');
    console.log('4. 重新尝试您的操作');

    // 自动跳转到登录页面
    if (window.location.pathname !== '/user/login') {
      console.log('🔄 正在跳转到登录页面...');
      setTimeout(() => {
        window.location.href = '/user/login';
      }, 2000);
    }

    console.groupEnd();
  }
};

// 在开发环境下将调试工具挂载到 window 对象
if (process.env.NODE_ENV === 'development') {
  (window as any).AuthDebug = AuthDebug;
  console.log('🛠️ AuthDebug 工具已挂载到 window.AuthDebug');
}
