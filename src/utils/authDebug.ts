/**
 * 认证调试工具
 * 用于检查和调试认证相关问题
 */

export const AuthDebug = {
  /**
   * 检查 JWT token 状态
   */
  checkToken() {
    const token = localStorage.getItem('jwt');
    console.group('🔐 JWT Token 状态检查');
    
    if (!token) {
      console.error('❌ 未找到 JWT token');
      console.log('💡 请先登录获取 token');
      console.groupEnd();
      return false;
    }
    
    console.log('✅ JWT token 存在');
    console.log('📝 Token 长度:', token.length);
    console.log('🔍 Token 前缀:', token.substring(0, 20) + '...');
    
    // 尝试解析 JWT payload (不验证签名)
    try {
      const parts = token.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(atob(parts[1]));
        console.log('📋 Token payload:', payload);
        
        // 检查过期时间
        if (payload.exp) {
          const expDate = new Date(payload.exp * 1000);
          const now = new Date();
          const isExpired = expDate < now;
          
          console.log('⏰ Token 过期时间:', expDate.toLocaleString());
          console.log('🕐 当前时间:', now.toLocaleString());
          console.log(isExpired ? '❌ Token 已过期' : '✅ Token 未过期');
          
          if (isExpired) {
            console.log('💡 请重新登录获取新的 token');
            console.groupEnd();
            return false;
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ 无法解析 token payload:', error);
    }
    
    console.groupEnd();
    return true;
  },

  /**
   * 测试 GraphQL 连接
   */
  async testGraphQLConnection() {
    console.group('🌐 GraphQL 连接测试');
    
    try {
      const response = await fetch('/server/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: '{ __schema { types { name } } }'
        })
      });
      
      if (response.ok) {
        console.log('✅ GraphQL 服务连接正常');
        const data = await response.json();
        console.log('📊 可用类型数量:', data.data?.__schema?.types?.length || 0);
      } else {
        console.error('❌ GraphQL 服务连接失败:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('❌ GraphQL 连接错误:', error);
    }
    
    console.groupEnd();
  },

  /**
   * 测试认证的 GraphQL 请求
   */
  async testAuthenticatedRequest() {
    console.group('🔒 认证请求测试');
    
    const token = localStorage.getItem('jwt');
    if (!token) {
      console.error('❌ 无法测试：未找到 JWT token');
      console.groupEnd();
      return;
    }
    
    try {
      const response = await fetch('/server/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          query: `{
            uaAuthorityInfos(needPage: true, pageSize: 1, pageNum: 1) {
              ok
              code
              message
            }
          }`
        })
      });
      
      const data = await response.json();
      
      if (response.ok && !data.errors) {
        console.log('✅ 认证请求成功');
        console.log('📊 响应数据:', data);
      } else {
        console.error('❌ 认证请求失败');
        console.error('📄 响应状态:', response.status);
        console.error('📋 错误信息:', data.errors || data);
        
        if (data.errors?.[0]?.message === '无permissions') {
          console.log('💡 建议：检查用户权限或重新登录');
        }
      }
    } catch (error) {
      console.error('❌ 认证请求错误:', error);
    }
    
    console.groupEnd();
  },

  /**
   * 完整的认证状态检查
   */
  async fullCheck() {
    console.log('🚀 开始完整的认证状态检查...');
    
    this.checkToken();
    await this.testGraphQLConnection();
    await this.testAuthenticatedRequest();
    
    console.log('✨ 认证状态检查完成');
  },

  /**
   * 清除认证信息
   */
  clearAuth() {
    localStorage.removeItem('jwt');
    console.log('🧹 已清除 JWT token，请重新登录');
  }
};

// 在开发环境下将调试工具挂载到 window 对象
if (process.env.NODE_ENV === 'development') {
  (window as any).AuthDebug = AuthDebug;
  console.log('🛠️ AuthDebug 工具已挂载到 window.AuthDebug');
}
