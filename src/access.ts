/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
export default function access(initialState: { currentUser?: API.CurrentUser } | undefined) {
  const { currentUser } = initialState ?? {};
  return {
    hasRole: (data: any)=>{
      const rtn = currentUser && currentUser.roles?.some((item: string)=>item.toLowerCase()===data.requestRole?.toLowerCase())
      console.log('hasRole', data, rtn);
      // return currentUser && currentUser.roles?.some((item: string)=>item.toLowerCase()===data.requestRole?.toLowerCase())
      return rtn;
    },
    // hasRoleAdmin: currentUser && currentUser.roles?.some((item: string)=>item.toLowerCase()==='admin'),
    // hasRoleUser: currentUser && currentUser.roles?.some((item: string)=>item.toLowerCase()==='user'),
    // hasRoleAgent: currentUser && currentUser.roles?.some((item: string)=>item.toLowerCase()==='agent'),
    hasAuthority: (data: any)=>{
      const rtn = currentUser && currentUser.permissions?.some((item: string)=>item.toLowerCase()===data.requestAuthority?.toLowerCase())
      console.log('hasAuthority', data, rtn);
      // return currentUser && currentUser.permissions?.some((item: string)=>item.toLowerCase()===data.requestAuthority?.toLowerCase())
      return rtn;
    },
    // hasAuthUa: currentUser && currentUser.permissions?.some((item: string)=>item.toLowerCase()==='ua'),
    // hasAuthUaAuthorityInfo: currentUser && currentUser.permissions?.some((item: string)=>item.toLowerCase()==='ua-authority-info'),
    // hasAuthUaRoleInfo: currentUser && currentUser.permissions?.some((item: string)=>item.toLowerCase()==='ua-role-info'),
  };
}
