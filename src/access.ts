/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
export default function access(initialState: { currentUser?: API.CurrentUser } | undefined) {
  const { currentUser } = initialState ?? {};
  return {
    hasRole: (data: any)=>currentUser && currentUser.roles?.some((item: string)=>item.toLowerCase()===data.requestRole?.toLowerCase()),
    // hasRoleAdmin: currentUser && currentUser.roles?.some((item: string)=>item.toLowerCase()==='admin'),
    // hasRoleUser: currentUser && currentUser.roles?.some((item: string)=>item.toLowerCase()==='user'),
    // hasRoleAgent: currentUser && currentUser.roles?.some((item: string)=>item.toLowerCase()==='agent'),
    hasAuthority: (data: any)=>currentUser && currentUser.permissions?.some((item: string)=>item.toLowerCase()===data.requestAuthority?.toLowerCase()),
    // hasAuthUa: currentUser && currentUser.permissions?.some((item: string)=>item.toLowerCase()==='ua'),
    // hasAuthUaAuthorityInfo: currentUser && currentUser.permissions?.some((item: string)=>item.toLowerCase()==='ua-authority-info'),
    // hasAuthUaRoleInfo: currentUser && currentUser.permissions?.some((item: string)=>item.toLowerCase()==='ua-role-info'),
  };
}
