import type { ActionType, ProColumns } from '@ant-design/pro-components';
import type { RecordKey } from '@ant-design/pro-utils/lib/useEditableArray';
import { PageContainer, EditableProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import {
  uaAuthorityInfos,
  // insertUaAuthorityInfo,
  updateUaAuthorityInfo,
  deleteUaAuthorityInfo,
} from '@/services/ua/api';
// import { useLocation } from 'umi';
import { useHistory } from 'umi';
import React, { useRef, useState } from 'react';

const AuthorityInfo: React.FC = () => {
  const [parentValue, setParentValue] = useState([{ parentId: -1, parentFullName: '' }]);

  const actionRef = useRef<ActionType>();

  // const location = useLocation();

  // location.query.parentId = location.query.parentId || -1;

  const history = useHistory();

  const columns: ProColumns<RestAPI.UaAuthorityInfo>[] = [
    // {
    //   dataIndex: 'id',
    //   valueType: 'indexBorder',
    //   width: 48,
    // },
    {
      title: '权限名称',
      dataIndex: 'name',
      ellipsis: true,
    },
    // {
    //   title: '完整权限',
    //   dataIndex: 'fullName',
    //   ellipsis: true,
    // },
    {
      title: '权限代码',
      dataIndex: 'uri',
      ellipsis: true,
    },
    // {
    //   title: '分组',
    //   dataIndex: 'groupName',
    //   ellipsis: true,
    // },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <a
          key="view"
          onClick={() => {
            // console.log(
            //   '查看下级权限',
            //   '/admin/authority?parentId=' +
            //     record.id +
            //     (record.fullName ? '&parentFullName=' + record.fullName : ''),
            // );
            // history.push(
            //   '/admin/authority?parentId=' +
            //     record.id +
            //     (record.fullName ? '&parentFullName=' + record.fullName : ''),
            // );
            parentValue.unshift({
              parentId: record.id,
              parentFullName: record.fullName ? record.fullName : '',
            });
            console.log('查看下级权限', parentValue[0]);
            setParentValue(parentValue);
            actionRef.current?.reload(true);
            history.push('/admin/authority');
          }}
        >
          查看下级权限
        </a>,
        // <TableDropdown
        //   key="actionGroup"
        //   onSelect={() => action?.reload()}
        //   menus={[
        //     { key: 'copy', name: '复制' },
        //     { key: 'delete', name: '删除' },
        //   ]}
        // />,
      ],
    },
  ];

  // const actionRef = useRef<ActionType>();

  // const location = useLocation();

  // location.query.parentId = location.query.parentId || -1;

  return (
    <PageContainer
      header={{
        title: '权限管理',
        breadcrumb: {},
      }}
    >
      <EditableProTable<RestAPI.UaAuthorityInfo>
        search={false}
        columns={columns}
        actionRef={actionRef}
        // params={location.query}
        // params={parentValue}
        request={async (params = {}, sort, filter) => {
          console.log(
            'RestAPI.UaAuthorityInfo location.query',
            // JSON.stringify(location.query).replaceAll('"', "'"),
            JSON.stringify(parentValue[0]).replaceAll('"', "'"),
          );
          console.log('RestAPI.UaAuthorityInfo', params, sort, filter);
          try {
          const rtn = await uaAuthorityInfos(
            true,
            params.pageSize,
            params.current,
            // JSON.stringify(location.query).replaceAll('"', "'"),
            JSON.stringify(parentValue[0]).replaceAll('"', "'"),
          );

          console.log('RestAPI.UaAuthorityInfo return', rtn);
          return {
            data: rtn.data.uaAuthorityInfos.data.list,
            page: rtn.data.uaAuthorityInfos.data.pageNum,
            success: rtn.data.uaAuthorityInfos.ok,
            total: rtn.data.uaAuthorityInfos.data.totalCount,
          };
          } catch (error) {
            console.log('RestAPI.UaAuthorityInfo error', error);
            return {
              data: [],
              page: 1,
              success: false,
              total: 0,
            };
          }
        }}
        recordCreatorProps={false}
        editable={{
          type: 'single',
          onSave: async (
            key: RecordKey,
            record: RestAPI.UaAuthorityInfo,
            originRow: RestAPI.UaAuthorityInfo,
            newLineConfig?: NewLineConfig,
          ) => {
            console.log('onSave', record, originRow);
            await updateUaAuthorityInfo(
              record.id,
              record.parentId,
              record.name,
              record.uri,
              record.type,
            );
            actionRef.current?.reload(true);
          },
          onDelete: async (key: RecordKey, row: RestAPI.UaAuthorityInfo) => {
            await deleteUaAuthorityInfo(row.id);
            actionRef.current?.reload(true);
          },
        }}
        rowKey="id"
        pagination={{
          pageSize: 10,
          onChange: (page) => console.log(page),
        }}
        // headerTitle={location.query.parentFullName || ''}
        // headerTitle={() => {
        //   console.log('headerTitle', parentValue[0]);
        //   return parentValue[0].parentFullName;
        // }}
        headerTitle={parentValue[0].parentFullName}
        toolBarRender={() => [
          // location.query.parentFullName ? (
          parentValue[0].parentId > 0 ? (
            <Button
              key="button"
              type="primary"
              onClick={() => {
                console.log('返回上级权限');
                history.goBack();
                parentValue.shift();
                setParentValue(parentValue);
                actionRef.current?.reload(true);
              }}
            >
              返回上级权限
            </Button>
          ) : (
            ''
          ),
          <Button
            type="primary"
            onClick={() => {
              actionRef.current?.addEditRecord?.({
                id: (Math.random() * 1000000).toFixed(0),
                // parentId: location.query.parentId,
                parentId: parentValue[0].parentId,
                type: 2,
              });
            }}
          >
            新增
          </Button>,
        ]}
      />
    </PageContainer>
  );
};

export default AuthorityInfo;
