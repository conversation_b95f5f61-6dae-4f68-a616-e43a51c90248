import { ActionType, ProColumns } from '@ant-design/pro-components';
import type { RecordKey } from '@ant-design/pro-utils/lib/useEditableArray';
import { PageContainer, EditableProTable } from '@ant-design/pro-components';
import {
  uaRoleInfos,
  // insertUaRoleInfo,
  updateUaRoleInfo,
  deleteUaRoleInfo,
  uaRoleAuthorityRelations,
  setUaRoleAuthorityRelations,
  uaAuthorityInfos,
} from '@/services/ua/api';
// import { useLocation } from 'umi';
import { useHistory } from 'umi';
import React, { useRef, useState } from 'react';
import type { ProFormInstance } from '@ant-design/pro-components';
import { ModalForm, ProFormTreeSelect } from '@ant-design/pro-components';
import { Button, message, TreeSelect } from 'antd';

// const waitTime = (time: number = 100) => {
//   return new Promise((resolve) => {
//     setTimeout(() => {
//       resolve(true);
//     }, time);
//   });
// };

const { SHOW_PARENT } = TreeSelect;

const RoleInfo: React.FC = () => {
  const [roleValue, setRoleValue] = useState({ roleId: -1, roleName: '' });
  const [authoritysInitValue, setAuthoritysInitValue] = useState([]);

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  // const location = useLocation();

  // location.query.roleId = location.query.roleId || -1;

  const history = useHistory();

  const columns: ProColumns<RestAPI.UaRoleInfo>[] = [
    // {
    //   dataIndex: 'id',
    //   valueType: 'indexBorder',
    //   width: 48,
    // },
    {
      title: '角色名称',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '角色编号',
      dataIndex: 'code',
      ellipsis: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <a
          key="view"
          onClick={() => {
            // console.log(
            //   '设置角色权限',
            //   '/admin/role?roleId=' + record.id + (record.name ? '&roleName=' + record.name : ''),
            // );
            // history.push(
            //   '/admin/role?roleId=' + record.id + (record.name ? '&roleName=' + record.name : ''),
            // );
            roleValue.roleId = record.id;
            roleValue.roleName = record.name ? record.name : '';
            setRoleValue(roleValue);
            actionRef.current?.reload(true);
            history.push('/admin/role');
          }}
        >
          设置角色权限
        </a>,
        // <TableDropdown
        //   key="actionGroup"
        //   onSelect={() => action?.reload()}
        //   menus={[
        //     { key: 'copy', name: '复制' },
        //     { key: 'delete', name: '删除' },
        //   ]}
        // />,
      ],
    },
  ];

  const columns2: ProColumns<RestAPI.UaRoleAuthorityRelation>[] = [
    // {
    //   dataIndex: 'id',
    //   valueType: 'indexBorder',
    //   width: 48,
    // },
    {
      title: '角色权限',
      dataIndex: 'fullName',
      ellipsis: true,
    },
    // {
    //   title: '操作',
    //   valueType: 'option',
    //   key: 'option',
    //   render: (text, record, _, action) => [
    //     <a
    //       key="delete"
    //       onClick={() => {
    //         // action?.startEditable?.(record.id);
    //       }}
    //     >
    //       删除
    //     </a>,
    //     // <a
    //     //   key="view"
    //     //   onClick={() => {
    //     //     console.log(
    //     //       '设置角色权限',
    //     //       '/admin/role?roleId=' + record.id + (record.name ? '&roleName=' + record.name : ''),
    //     //     );
    //     //     history.push(
    //     //       '/admin/role?roleId=' + record.id + (record.name ? '&roleName=' + record.name : ''),
    //     //     );
    //     //   }}
    //     // >
    //     //   设置角色权限
    //     // </a>,
    //     // <TableDropdown
    //     //   key="actionGroup"
    //     //   onSelect={() => action?.reload()}
    //     //   menus={[
    //     //     { key: 'copy', name: '复制' },
    //     //     { key: 'delete', name: '删除' },
    //     //   ]}
    //     // />,
    //   ],
    // },
  ];

  // const actionRef = useRef<ActionType>();

  // const location = useLocation();

  // location.query.parentId = location.query.parentId || -1;

  return (
    <PageContainer
      header={{
        title: '角色管理',
        breadcrumb: {},
      }}
    >
      <EditableProTable<RestAPI.UaRoleInfo>
        search={false}
        columns={columns}
        actionRef={actionRef}
        // params={location.query}
        request={async (params = {}, sort, filter) => {
          // console.log('RestAPI.uaRoleInfos location.query', location.query);
          console.log('RestAPI.uaRoleInfos', params, sort, filter);
          const rtn = await uaRoleInfos(true, params.pageSize, params.current, '');
          console.log('RestAPI.uaRoleInfos return', rtn);
          return {
            data: rtn.data.uaRoleInfos.data.list,
            page: rtn.data.uaRoleInfos.data.pageNum,
            success: rtn.data.uaRoleInfos.ok,
            total: rtn.data.uaRoleInfos.data.totalCount,
          };
        }}
        recordCreatorProps={false}
        editable={{
          type: 'single',
          onSave: async (
            key: RecordKey,
            record: RestAPI.UaRoleInfo,
            originRow: RestAPI.UaRoleInfo,
            newLineConfig?: NewLineConfig,
          ) => {
            console.log('onSave', record, originRow);
            await updateUaRoleInfo(record.id, record.name, record.code);
            actionRef.current?.reload(true);
          },
          onDelete: async (key: RecordKey, row: RestAPI.UaRoleInfo) => {
            await deleteUaRoleInfo(row.id);
            actionRef.current?.reload(true);
            // if (location.query && location.query.roleId && location.query.roleId == row.id) {
            if (roleValue.roleId == row.id) {
              history.replace('/admin/role');
              // history.push('/admin/role');
            }
          },
        }}
        rowKey="id"
        pagination={{
          pageSize: 10,
          onChange: (page) => console.log(page),
        }}
        headerTitle={roleValue.roleName || ''}
        toolBarRender={() => [
          // location.query.parentFullName ? (
          //   <Button
          //     key="button"
          //     type="primary"
          //     onClick={() => {
          //       console.log('返回上级权限');
          //       history.goBack();
          //     }}
          //   >
          //     返回上级权限
          //   </Button>
          // ) : (
          //   ''
          // ),
          <Button
            type="primary"
            onClick={() => {
              actionRef.current?.addEditRecord?.({
                id: (Math.random() * 1000000).toFixed(0),
              });
            }}
          >
            新增
          </Button>,
        ]}
      />
      {roleValue.roleId > 0 ? (
        <EditableProTable<RestAPI.UaRoleAuthorityRelation>
          search={false}
          columns={columns2}
          actionRef={actionRef}
          // params={location.query}
          request={async (params = {}, sort, filter) => {
            // console.log('RestAPI.UaRoleAuthorityRelation location.query', location.query);
            console.log('RestAPI.UaRoleAuthorityRelation', params, sort, filter);
            const rtn = await uaRoleAuthorityRelations(
              true,
              params.pageSize,
              params.current,
              // JSON.stringify(location.query).replaceAll('"', "'"),
              JSON.stringify(roleValue).replaceAll('"', "'"),
            );
            console.log('RestAPI.UaRoleAuthorityRelation return', rtn);

            const initValue: string[] = [];
            rtn.data.uaRoleAuthorityRelations.data.list.forEach((value, index, self) => {
              initValue.push(value.authorityId);
            });
            setAuthoritysInitValue(initValue);
            console.log('setAuthoritysInitValue', initValue);

            console.log('setFieldsValue1', initValue);
            formRef?.current?.setFieldsValue({
              authoritys: initValue,
            });

            return {
              data: rtn.data.uaRoleAuthorityRelations.data.list,
              page: rtn.data.uaRoleAuthorityRelations.data.pageNum,
              success: rtn.data.uaRoleAuthorityRelations.ok,
              total: rtn.data.uaRoleAuthorityRelations.data.totalCount,
            };
          }}
          recordCreatorProps={false}
          // editable={{
          //   type: 'single',
          //   onSave: async (
          //     key: RecordKey,
          //     record: RestAPI.UaRoleInfo,
          //     originRow: RestAPI.UaRoleInfo,
          //     newLineConfig?: NewLineConfig,
          //   ) => {
          //     console.log('onSave', record, originRow);
          //     await updateUaRoleInfo(record.id, record.name, record.code);
          //     actionRef.current?.reload(true);
          //   },
          //   onDelete: async (key: RecordKey, row: RestAPI.UaRoleInfo) => {
          //     await deleteUaRoleInfo(row.id);
          //     actionRef.current?.reload(true);
          //     // if (location.query && location.query.roleId && location.query.roleId == row.id) {
          //     if (roleValue.roleId == row.id) {
          //         history.replace('/admin/role');
          //     }
          //   },
          // }}
          rowKey="id"
          pagination={{
            pageSize: 10,
            onChange: (page) => console.log(page),
          }}
          headerTitle={(roleValue.roleName || '') + ' 角色权限'}
          toolBarRender={() => [
            <ModalForm
              formRef={formRef}
              title={(roleValue.roleName || '') + ' 角色权限 设置'}
              trigger={<Button type="primary">设置角色权限</Button>}
              onFinish={async (values) => {
                // await waitTime(2000);
                console.log(values);
                console.log(values.authoritys.toString());

                await setUaRoleAuthorityRelations(roleValue.roleId, values.authoritys.toString());
                actionRef.current?.reload(true);

                message.success('提交成功');
                return true;
              }}
              initialValues={{
                authoritys: authoritysInitValue,
              }}
            >
              <ProFormTreeSelect
                name="authoritys"
                placeholder="请选择角色对应权限"
                allowClear
                // width={330}
                // secondary
                request={async () => {
                  const rtn = await uaAuthorityInfos(false, 0, 0, '');
                  console.log('RestAPI.uaAuthorityInfos return', rtn);
                  let returnList = [];
                  rtn.data.uaAuthorityInfos.data.list.forEach((value, index, self) => {
                    returnList.push({
                      id: value.id,
                      pId: value.parentId,
                      value: value.id,
                      title: value.fullName,
                    });
                  });
                  console.log('RestAPI.uaAuthorityInfos for ProFormTreeSelect', returnList);
                  return returnList;
                }}
                // tree-select args
                fieldProps={{
                  showArrow: false,
                  treeCheckable: true,
                  showCheckedStrategy: SHOW_PARENT,
                  treeDataSimpleMode: true,
                  treeDefaultExpandAll: true,
                  fieldNames: {
                    label: 'title',
                  },
                }}
              />
            </ModalForm>,
            // location.query.parentFullName ? (
            //   <Button
            //     key="button"
            //     type="primary"
            //     onClick={() => {
            //       console.log('返回上级权限');
            //       history.goBack();
            //     }}
            //   >
            //     返回上级权限
            //   </Button>
            // ) : (
            //   ''
            // ),
            // <Button
            //   type="primary"
            //   onClick={() => {
            //     actionRef.current?.addEditRecord?.({
            //       id: (Math.random() * 1000000).toFixed(0),
            //     });
            //   }}
            // >
            //   新增
            // </Button>,
          ]}
        />
      ) : (
        ''
      )}
    </PageContainer>
  );
};

export default RoleInfo;
