import { ActionType, ProColumns } from '@ant-design/pro-components';
import type { RecordKey } from '@ant-design/pro-utils/lib/useEditableArray';
import { PageContainer, EditableProTable } from '@ant-design/pro-components';
import {
  uaUserInfos,
  updateUaUserInfo,
  deleteUaUserInfo,
  uaUserRoleRelations,
  setUaUserRoleRelations,
  uaRoleInfos,
} from '@/services/ua/api';
// import { useLocation } from 'umi';
import { useHistory } from 'umi';
import React, { useRef, useState } from 'react';
import type { ProFormInstance } from '@ant-design/pro-components';
import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { Button, message } from 'antd';

// const waitTime = (time: number = 100) => {
//   return new Promise((resolve) => {
//     setTimeout(() => {
//       resolve(true);
//     }, time);
//   });
// };

const UserInfo: React.FC = () => {
  const [userValue, setUserValue] = useState({ userId: -1, userName: '' });
  const [rolesInitValue, setRolesInitValue] = useState([]);

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  // const location = useLocation();

  // location.query.roleId = location.query.roleId || -1;

  const history = useHistory();

  const columns: ProColumns<RestAPI.UaUserInfo>[] = [
    // {
    //   dataIndex: 'id',
    //   valueType: 'indexBorder',
    //   width: 48,
    // },
    {
      title: '用户名',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '用户类别',
      dataIndex: 'type',
      ellipsis: true,
      valueType: 'select',
      valueEnum: {
        1: { text: '管理员' },
        2: { text: '特工' },
        3: { text: '客户' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <a
          key="view"
          onClick={() => {
            // console.log(
            //   '设置角色权限',
            //   '/admin/role?roleId=' + record.id + (record.name ? '&roleName=' + record.name : ''),
            // );
            // history.push(
            //   '/admin/role?roleId=' + record.id + (record.name ? '&roleName=' + record.name : ''),
            // );
            userValue.userId = record.id;
            userValue.userName = record.name ? record.name : '';
            setUserValue(userValue);
            actionRef.current?.reload(true);
            history.push('/admin/user');
          }}
        >
          设置用户角色
        </a>,
        // <TableDropdown
        //   key="actionGroup"
        //   onSelect={() => action?.reload()}
        //   menus={[
        //     { key: 'copy', name: '复制' },
        //     { key: 'delete', name: '删除' },
        //   ]}
        // />,
      ],
    },
  ];

  const columns2: ProColumns<RestAPI.UaUserRoleRelation>[] = [
    // {
    //   dataIndex: 'id',
    //   valueType: 'indexBorder',
    //   width: 48,
    // },
    {
      title: '用户角色',
      dataIndex: 'fullName',
      ellipsis: true,
    },
    // {
    //   title: '操作',
    //   valueType: 'option',
    //   key: 'option',
    //   render: (text, record, _, action) => [
    //     <a
    //       key="delete"
    //       onClick={() => {
    //         // action?.startEditable?.(record.id);
    //       }}
    //     >
    //       删除
    //     </a>,
    //     // <a
    //     //   key="view"
    //     //   onClick={() => {
    //     //     console.log(
    //     //       '设置角色权限',
    //     //       '/admin/role?roleId=' + record.id + (record.name ? '&roleName=' + record.name : ''),
    //     //     );
    //     //     history.push(
    //     //       '/admin/role?roleId=' + record.id + (record.name ? '&roleName=' + record.name : ''),
    //     //     );
    //     //   }}
    //     // >
    //     //   设置角色权限
    //     // </a>,
    //     // <TableDropdown
    //     //   key="actionGroup"
    //     //   onSelect={() => action?.reload()}
    //     //   menus={[
    //     //     { key: 'copy', name: '复制' },
    //     //     { key: 'delete', name: '删除' },
    //     //   ]}
    //     // />,
    //   ],
    // },
  ];

  // const actionRef = useRef<ActionType>();

  // const location = useLocation();

  // location.query.parentId = location.query.parentId || -1;

  return (
    <PageContainer
      header={{
        title: '用户管理',
        breadcrumb: {},
      }}
    >
      <EditableProTable<RestAPI.UaUserInfo>
        search={false}
        columns={columns}
        actionRef={actionRef}
        // params={location.query}
        request={async (params = {}, sort, filter) => {
          // console.log('RestAPI.uaUserInfos location.query', location.query);
          console.log('RestAPI.uaUserInfos', params, sort, filter);
          const rtn = await uaUserInfos(true, params.pageSize, params.current, '');
          console.log('RestAPI.uaUserInfos return', rtn);
          return {
            data: rtn.data.uaUserInfos.data.list,
            page: rtn.data.uaUserInfos.data.pageNum,
            success: rtn.data.uaUserInfos.ok,
            total: rtn.data.uaUserInfos.data.totalCount,
          };
        }}
        recordCreatorProps={false}
        editable={{
          type: 'single',
          onSave: async (
            key: RecordKey,
            record: RestAPI.UaUserInfo,
            originRow: RestAPI.UaUserInfo,
            newLineConfig?: NewLineConfig,
          ) => {
            console.log('onSave', record, originRow);
            if (newLineConfig) {
              await updateUaUserInfo(record.id, record.name, '123456', record.type);
            } else {
              await updateUaUserInfo(record.id, record.name, '', record.type);
            }
            actionRef.current?.reload(true);
          },
          onDelete: async (key: RecordKey, row: RestAPI.UaUserInfo) => {
            await deleteUaUserInfo(row.id);
            actionRef.current?.reload(true);
            // if (location.query && location.query.roleId && location.query.roleId == row.id) {
            if (userValue.userId == row.id) {
              history.replace('/admin/user');
              // history.push('/admin/user');
            }
          },
        }}
        rowKey="id"
        pagination={{
          pageSize: 10,
          onChange: (page) => console.log(page),
        }}
        headerTitle={userValue.userName || ''}
        toolBarRender={() => [
          // location.query.parentFullName ? (
          //   <Button
          //     key="button"
          //     type="primary"
          //     onClick={() => {
          //       console.log('返回上级权限');
          //       history.goBack();
          //     }}
          //   >
          //     返回上级权限
          //   </Button>
          // ) : (
          //   ''
          // ),
          <Button
            type="primary"
            onClick={() => {
              actionRef.current?.addEditRecord?.({
                id: (Math.random() * 1000000).toFixed(0),
                password: '123456',
              });
            }}
          >
            新增
          </Button>,
        ]}
      />
      {userValue.userId > 0 ? (
        <EditableProTable<RestAPI.UaUserRoleRelation>
          search={false}
          columns={columns2}
          actionRef={actionRef}
          // params={location.query}
          request={async (params = {}, sort, filter) => {
            // console.log('RestAPI.UaUserRoleRelation location.query', location.query);
            console.log('RestAPI.UaUserRoleRelation', params, sort, filter);
            const rtn = await uaUserRoleRelations(
              true,
              params.pageSize,
              params.current,
              // JSON.stringify(location.query).replaceAll('"', "'"),
              JSON.stringify(userValue).replaceAll('"', "'"),
            );
            console.log('RestAPI.UaUserRoleRelation return', rtn);

            const initValue: string[] = [];
            rtn.data.uaUserRoleRelations.data.list.forEach((value, index, self) => {
              initValue.push(value.roleId);
            });
            setRolesInitValue(initValue);
            console.log('setRolesInitValue', initValue);

            console.log('setFieldsValue1', initValue);
            formRef?.current?.setFieldsValue({
              roles: initValue,
            });

            return {
              data: rtn.data.uaUserRoleRelations.data.list,
              page: rtn.data.uaUserRoleRelations.data.pageNum,
              success: rtn.data.uaUserRoleRelations.ok,
              total: rtn.data.uaUserRoleRelations.data.totalCount,
            };
          }}
          recordCreatorProps={false}
          // editable={{
          //   type: 'single',
          //   onSave: async (
          //     key: RecordKey,
          //     record: RestAPI.UaUserInfo,
          //     originRow: RestAPI.UaUserInfo,
          //     newLineConfig?: NewLineConfig,
          //   ) => {
          //     console.log('onSave', record, originRow);
          //     await updateUaUserInfo(record.id, record.name, record.code);
          //     actionRef.current?.reload(true);
          //   },
          //   onDelete: async (key: RecordKey, row: RestAPI.UaUserInfo) => {
          //     await deleteUaUserInfo(row.id);
          //     actionRef.current?.reload(true);
          //     // if (location.query && location.query.userId && location.query.userId == row.id) {
          //     if (userValue.userId == row.id) {
          //         history.replace('/admin/user');
          //     }
          //   },
          // }}
          rowKey="id"
          pagination={{
            pageSize: 10,
            onChange: (page) => console.log(page),
          }}
          headerTitle={(userValue.userName || '') + ' 用户角色'}
          toolBarRender={() => [
            <ModalForm
              formRef={formRef}
              title={(userValue.userName || '') + ' 用户角色 设置'}
              trigger={<Button type="primary">设置用户角色</Button>}
              onFinish={async (values) => {
                // await waitTime(2000);
                console.log(values);
                console.log(values.roles.toString());

                await setUaUserRoleRelations(userValue.userId, values.roles.toString());
                actionRef.current?.reload(true);

                message.success('提交成功');
                return true;
              }}
              initialValues={{
                roles: rolesInitValue,
              }}
            >
              <ProFormSelect
                name="roles"
                placeholder="请选择用户对应角色"
                allowClear
                // width={330}
                // secondary
                request={async () => {
                  const rtn = await uaRoleInfos(false, 0, 0, '');
                  console.log('RestAPI.uaRoleInfos return', rtn);
                  let returnList = [];
                  rtn.data.uaRoleInfos.data.list.forEach((value, index, self) => {
                    returnList.push({
                      value: value.id,
                      label: value.name,
                    });
                  });
                  console.log('RestAPI.uaRoleInfos for ProFormSelect', returnList);
                  return returnList;
                }}
                // select args
                fieldProps={{
                  showArrow: false,
                  mode: 'multiple',
                }}
              />
            </ModalForm>,
            // location.query.parentFullName ? (
            //   <Button
            //     key="button"
            //     type="primary"
            //     onClick={() => {
            //       console.log('返回上级权限');
            //       history.goBack();
            //     }}
            //   >
            //     返回上级权限
            //   </Button>
            // ) : (
            //   ''
            // ),
            // <Button
            //   type="primary"
            //   onClick={() => {
            //     actionRef.current?.addEditRecord?.({
            //       id: (Math.random() * 1000000).toFixed(0),
            //     });
            //   }}
            // >
            //   新增
            // </Button>,
          ]}
        />
      ) : (
        ''
      )}
    </PageContainer>
  );
};

export default UserInfo;
