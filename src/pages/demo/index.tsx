import type { ActionType, ProColumns } from '@ant-design/pro-components';
import type { RecordKey } from '@ant-design/pro-utils/lib/useEditableArray';
import { PageContainer } from '@ant-design/pro-components';
// import { Modal, Button, Input, Space, Tag } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import { Button, Space, Tag, List, Avatar, Tabs } from 'antd';
import { chats, chat, insertChat, updateChat, deleteChat, project } from '@/services/project/api';
import React, { useRef, useState, useEffect } from 'react';
import {
  ProFormInstance,
  ProForm,
  ProFormText,
  ProFormDateTimeRangePicker,
  ProFormTextArea,
  ProFormSelect,
  ProFormGroup,
} from '@ant-design/pro-components';
import { useModel, useRequest } from 'umi';
import ChatList from '@/components/chatList';
import ChatDetail from '@/components/chatDetail';

const { TabPane } = Tabs;

// const chatTextRight = {
//   flex-direction: 'row-reverse',
//   text-align: 'right',
// }

// const data = [
//   {
//     name: '语雀的天空',
//     image:
//       'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg',
//     desc: '我是一条测试的描述',
//   },
//   {
//     name: 'Ant Design',
//     image:
//       'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg',
//     desc: '我是一条测试的描述',
//   },
//   {
//     name: '蚂蚁金服体验科技',
//     image:
//       'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg',
//     desc: '我是一条测试的描述',
//   },
//   {
//     name: 'TechUI',
//     image:
//       'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg',
//     desc: '我是一条测试的描述',
//   },
// ];

// type ChatListProps = {
//   projectData: any;
//   chatType: number;
//   from: any;
//   to: any;
// };

// const ChatList: React.FC<ChatListProps> = (props) => {
//   const { projectData, chatType, from, to } = props;
//   const [listData, setListData] = useState([]);
//   const [chatFormValue, setChatFormValue] = useState({});
//   const formRef = useRef<ProFormInstance>();

//   let timerId = null;

//   const getListData = async () => {
//     const rtn = await chats(false, 0, 0, `{chatType: ${chatType}, projectId: ${projectData.id}}`);
//     console.log('chats', rtn);
//     setListData(rtn.data.chats.data.list);
//   };

//   const autoRefresh = () => {
//     console.log('autoRefresh');
//     if (timerId) {
//       clearTimeout(timerId);
//       timerId = null;
//       console.log('clearTimeout', timerId);
//     }
//     if (projectData.id > 0) {
//       getListData();
//       timerId = setTimeout(autoRefresh, 30000);
//       console.log('setTimeout', timerId);
//     }
//   };

//   useEffect(() => {
//     autoRefresh();
//   }, [projectData]);

//   const sendMessage = async () => {
//     const rtn = await insertChat(
//       projectData.id,
//       chatType,
//       from.id,
//       from.type,
//       to.id,
//       to.type,
//       1,
//       chatFormValue.message,
//     );
//     formRef?.current?.setFieldsValue({
//       message: '',
//     });
//     getListData();
//   };

//   return (
//     <ProCard>
//       <List
//         // itemLayout="horizontal"
//         dataSource={listData}
//         renderItem={(item, index) => (
//           // {console.log('List', index);}
//           <List.Item>
//             <List.Item.Meta
//               style={
//                 item.fromType === from.type
//                   ? { flexDirection: 'row-reverse', textAlign: 'right' }
//                   : {}
//               }
//               // avatar={<Avatar src={item.image} style={{ marginLeft: '16px' }} />}
//               title={item.from.name}
//               description={item.message}
//             />
//           </List.Item>
//         )}
//       />
//       <ProForm
//         formRef={formRef}
//         submitter={false}
//         onFinish={async (values) => console.log(values)}
//         onValuesChange={(_, values) => {
//           console.log('onValuesChange', values);
//           setChatFormValue(values);
//         }}
//       >
//         <ProFormGroup>
//           <ProFormText width="lg" name="message" placeholder="请输入信息" />
//           <Button type="primary" onClick={sendMessage}>
//             发送消息
//           </Button>
//           <Button type="primary">发送图片</Button>
//           <Button type="primary">发送视频</Button>
//           <Button type="primary">发送文件</Button>
//         </ProFormGroup>
//       </ProForm>
//     </ProCard>
//   );
// };

const Demo: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  console.log('currentUser', currentUser);

  const [projectData, setProjectData] = useState({});

  useEffect(async () => {
    const rtn = await project(1);
    console.log('project', rtn);
    setProjectData(rtn.data.project.data);
  }, projectData);

  return (
    <PageContainer
      header={{
        title: 'Demo1',
        breadcrumb: {},
      }}
    >
      {projectData.id > 0 ? (
        <ProCard title={projectData.fullName}>
          <Tabs type="card">
            <TabPane tab="项目详情" key="1">
              <ChatDetail projectData={projectData} />
            </TabPane>
            <TabPane tab="客户" key="2">
              <ChatList
                projectData={projectData}
                chatType={2}
                from={{ id: currentUser?.id, type: 1 }}
                // to={{ id: 2, type: 2 }}
                to={{ id: 1, type: 3 }}
              />
            </TabPane>
          </Tabs>
        </ProCard>
      ) : (
        ''
      )}
      {/* <ProCard split="horizontal">
        <ChatDetail projectData={projectData} />
        <ChatList
          projectData={projectData}
          chatType={2}
          from={{ id: currentUser?.id, type: 1 }}
          // to={{ id: 2, type: 2 }}
          to={{ id: 1, type: 3 }}
        />
      </ProCard> */}
    </PageContainer>
  );
};

export default Demo;
