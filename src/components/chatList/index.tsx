import type { ActionType, ProColumns } from '@ant-design/pro-components';
import type { RecordKey } from '@ant-design/pro-utils/lib/useEditableArray';
import { PageContainer } from '@ant-design/pro-components';
// import { Modal, Button, Input, Space, Tag } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import { Button, Space, Tag, List, Avatar, Divider, Skeleton } from 'antd';
import { chats, chat, insertChat, updateChat, deleteChat } from '@/services/project/api';
import React, { useRef, useState, useEffect } from 'react';
import {
  ProFormInstance,
  ProForm,
  ProFormText,
  ProFormDateTimeRangePicker,
  ProFormTextArea,
  ProFormSelect,
  ProFormGroup,
} from '@ant-design/pro-components';
import { useModel, useRequest } from 'umi';
import InfiniteScroll from 'react-infinite-scroll-component';

type ChatListProps = {
  projectData: any;
  chatType: number;
  from: any;
  to: any;
};

const ChatList: React.FC<ChatListProps> = (props) => {
  const { projectData, chatType, from, to } = props;
  const [listData, setListData] = useState([]);
  const [pageNum, setPageNum] = useState(1);
  const [total, setTotal] = useState(0);
  const [chatFormValue, setChatFormValue] = useState({});
  const formRef = useRef<ProFormInstance>();

  let timerId = null;

  const getListData = async (pNum: number) => {
    const rtn = await chats(
      false,
      20,
      pNum,
      `{chatType: ${chatType}, projectId: ${projectData.id}}`,
    );
    // const rtn = await chats(
    //   false,
    //   5,
    //   pNum,
    //   `{chatType: ${chatType}, projectId: ${projectData.id}}`,
    // );
    console.log('chats', rtn);
    // setListData(listData.concat(rtn.data.chats.data.list));
    setListData(rtn.data.chats.data.list);
    setTotal(rtn.data.chats.data.totalCount);
  };

  const moreData = async () => {
    const newPageNum = pageNum + 1;
    setPageNum(newPageNum);
    await getListData(newPageNum);
    console.log('moreData', newPageNum);
  };

  const autoRefresh = () => {
    console.log('autoRefresh', timerId);
    if (timerId) {
      clearTimeout(timerId);
      timerId = null;
      console.log('clearTimeout', timerId);
    }
    if (projectData.id > 0) {
      getListData(pageNum);
      timerId = setTimeout(autoRefresh, 30000);
      console.log('setTimeout', timerId);
    }
  };

  useEffect(() => {
    autoRefresh();
    return () => {
      console.log('=============componentWillUnmount=============');
      if (timerId) {
        clearTimeout(timerId);
        timerId = null;
        console.log('clearTimeout', timerId);
      }
    };
  }, [projectData]);

  const sendMessage = async () => {
    const rtn = await insertChat(
      projectData.id,
      chatType,
      from.id,
      from.type,
      to.id,
      to.type,
      1,
      chatFormValue.message,
    );
    formRef?.current?.setFieldsValue({
      message: '',
    });
    getListData();
  };

  // const componentWillUnmount = () => {
  //   if (timerId) {
  //     clearTimeout(timerId);
  //     timerId = null;
  //     console.log('clearTimeout', timerId);
  //   }
  //   alert('componentWillUnmount');
  // };

  // componentWillUnmount = () => {
  //   // Clean up any timers, event listeners, etc.
  //   console.log('=============componentWillUnmount=============');
  // };

  return (
    <ProCard
    // style={{ height: '300' }}
    // id="scrollableDiv"
    >
      <div
        id="scrollableDiv"
        style={{
          height: 350,
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column-reverse',
        }}
      >
        <InfiniteScroll
          dataLength={listData.length}
          next={moreData}
          hasMore={listData.length < total}
          // loader={<h4>Loading...</h4>}
          // endMessage={<h4>End.</h4>}
          style={{ display: 'flex', flexDirection: 'column-reverse' }}
          scrollableTarget="scrollableDiv"
          // height={350}
        >
          <List
            // itemLayout="horizontal"
            dataSource={listData}
            renderItem={(item, index) => (
              // {console.log('List', index);}
              <List.Item
                style={Object.assign(
                  {},
                  index % 2 === 0 ? { backgroundColor: 'rgba(0, 0, 0, .05)' } : {},
                  { paddingLeft: '10px', paddingRight: '10px' },
                )}
              >
                <List.Item.Meta
                  style={
                    item.fromType === from.type
                      ? { flexDirection: 'row-reverse', textAlign: 'right' }
                      : {}
                  }
                  // avatar={<Avatar src={item.image} style={{ marginLeft: '16px' }} />}
                  title={
                    <div>
                      <big>{(item.from ? item.from.name : item.fromCustomer.name) + ' '}</big>
                      <small>
                        <small>{'(' + item.createTime + ')'}</small>
                      </small>
                    </div>
                  }
                  description={item.message}
                />
              </List.Item>
            )}
          />
        </InfiniteScroll>
      </div>

      <ProForm
        formRef={formRef}
        submitter={false}
        onFinish={async (values) => console.log(values)}
        onValuesChange={(_, values) => {
          console.log('onValuesChange', values);
          setChatFormValue(values);
        }}
      >
        <ProFormGroup>
          <ProFormText width="lg" name="message" placeholder="请输入信息" />
          <Button type="primary" onClick={sendMessage}>
            发送消息
          </Button>
          <Button type="primary">发送图片</Button>
          <Button type="primary">发送视频</Button>
          <Button type="primary">发送文件</Button>
        </ProFormGroup>
      </ProForm>
    </ProCard>
  );
};

export default ChatList;
