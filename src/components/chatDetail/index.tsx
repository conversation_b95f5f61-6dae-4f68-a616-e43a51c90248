import type { ActionType, ProColumns } from '@ant-design/pro-components';
// import type { RecordKey } from '@ant-design/pro-utils/lib/useEditableArray';
// import { PageContainer } from '@ant-design/pro-components';
// import { Modal, Button, Input, Space, Tag } from 'antd';
import { ProCard, ProTable } from '@ant-design/pro-components';
// import { Button, Space, Tag, List, Avatar } from 'antd';
// import { chats, chat, insertChat, updateChat, deleteChat } from '@/services/project/api';
import React, { useRef, useState, useEffect } from 'react';
// import {
//   ProFormInstance,
//   ProForm,
//   ProFormText,
//   ProFormDateTimeRangePicker,
//   ProFormTextArea,
//   ProFormSelect,
//   ProFormGroup,
// } from '@ant-design/pro-components';
// import { useModel, useRequest } from 'umi';

type DetailFieldItem = {
  key: string;
  value: string;
};

const columns: ProColumns<DetailFieldItem>[] = [
  {
    title: '字段名',
    dataIndex: 'key',
    ellipsis: true,
  },
  {
    title: '值',
    dataIndex: 'value',
    ellipsis: true,
  },
];

type ChatDetailProps = {
  projectData: any;
};

const ChatDetail: React.FC<ChatDetailProps> = (props) => {
  const { projectData } = props;

  return (
    <ProCard>
      <ProTable<DetailFieldItem>
        search={false}
        toolBarRender={false}
        columns={columns}
        showHeader={false}
        request={async (params = {}, sort, filter) => {
          console.log('DetailFieldItem', projectData);

          const list = [];

          list.push({
            key: '项目名称',
            value: projectData.name,
          });
          list.push({
            key: '项目完整名称',
            value: projectData.fullName,
          });
          list.push({
            key: '客户',
            value: projectData.customer.name,
          });
          // list.push({
          //   key: '管理员',
          //   value: projectData.admin.name,
          // });
          // list.push({
          //   key: '特工',
          //   value: projectData.agent.name,
          // });
          list.push({
            key: '执行开始时间',
            value: projectData.startDatetime,
          });
          list.push({
            key: '执行结束时间',
            value: projectData.endDatetime,
          });
          list.push({
            key: '执行地址',
            value: projectData.address,
          });
          list.push({
            key: '项目要求',
            value: projectData.requirements,
          });

          return Promise.resolve({
            data: list,
            // page: 1,
            success: true,
            // total: 0,
          });
        }}
        pagination={false}
      />
    </ProCard>
  );
};

export default ChatDetail;
