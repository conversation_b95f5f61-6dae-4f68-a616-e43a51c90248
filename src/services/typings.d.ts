// @ts-ignore
/* eslint-disable */

declare namespace RestAPI {
  type editStatus = {
    editType: string;
  }

  type ApiResult = {
    ok: boolean;
    code: number;
    message: string;
    data?: any;
  };

  type GeoPoint = {
    lat: Float;
    lng: Float;
  };

  type User = {
    id: number;
    username: string;
    // password: string;
    loginTime: string;
    roles: string[];
    permissions: string[];
    token: string;
  };

  type LoginParams = {
    username?: string;
    password?: string;
  };

  type UaUserInfo = {
    id: number;
    name: string;
    password: string;
    type: number;
    historyName: string;
    status: number;
    phone: string;
    email: string;
    remark: string;
  };

  type UaCustomerInfo = {
    id: number;
    name: string;
    contacts: string;
    phone: string;
  };

  type UaRoleInfo = {
    id: number;
    code: string;
    name: string;
    status: number;
    remark: string;
  };

  type UaRoleAuthorityRelation = {
    id: number;
    roleId: number;
    authorityId: number;
    role: any;
    authority: any;
    fullName: string;
  };

  type UaAuthorityInfo = {
    id: number;
    parentId: number;
    parent: any;
    name: string;
    uri: string;
    type: number;
    groupName: string;
    view: string;
    status: number;
    hide: boolean;
    icon: string;
    sort: number;
    remark: string;
    permTag: string;
    fullName: string;
  };

  type ECategorize = {
    id: number;
    parentId: number;
    name: string;
    sort: number;
    taskTemplateId: number;
    enabled: number;
    reviewResult: number;
    reviewId: number;
    fullName: string;
    parent: any;
    taskTemplat: any;
    review: any;
  };

  type EPricing = {
    id: number;
    type: number;
    points: number;
    categorizeId: number;
    enabled: number;
    reviewResult: number;
    reviewId: number;
    categorizeName: string;
    review: any;
  };

  type ECustomerServiceQA = {
    id: number;
    userId: number;
    question: string;
    answer: string;
    enabled: number;
    reviewResult: number;
    reviewId: number;
    createTime: string;
    user: any;
  };

  type ECustomerServiceOnline = {
    id: number;
    userId: number;
    senderId: number;
    message: string;
    image: number;
    received: number;
    createTime: string;
    user: any;
    sender: any;
    imageUrl: string;
  } & editStatus;

  type ETaskTemplate = {
    id: number;
    taskNameTemplate: string;
    taskDescribe: string;
    taskDescribe_taskDescribe: string;
  };

  type ETaskResourceTemplate = {
    id: number;
    taskTemplateId: number;
    resourceNameTemplate: string;
    resourceDescribe: string;
    resourceDescribe_resourceDescribe: string;
    resourceDescribe_level: string;
    resourceDescribe_resourceFormat: string;
    resourceDescribe_referResourceId: string;
    resourceDescribe_referResourceUrl: string;
  } & editStatus;

  type ETask = {
    id: number;
    userId: number;
    categorizeId: number;
    taskTemplateId: number;
    name: string;
    taskDescribe: string;
    startDatetime: string;
    endDatetime: string;
    location: GeoPoint;
    address: string;
    isPublic: number;
    enabled: number;
    reviewResult: number;
    reviewId: number;
    createStatus: number;
    publicStatus: number;
    executeStatus: number;
    user: any;
    categorize: any;
    taskTemplate: any;
    review: any;
    taskResource: any;
    taskPricing: any;
  }

  type ETaskResource = {
    id: number;
    taskId: number;
    taskResourceTemplateId: number;
    uploadPricingId: number;
    uploadPoints: number;
    name: string;
    resourceDescribe: string;
    level: number;
    resourceFormat: number;
    referResourceId: number;
    uploadResourceId: number;
    task: any;
    taskResourceTemplate: any;
    resource: any;
    resources: any;
    referResource: any;
    referResourceUrl: string;
  }

  type ETaskPricing = {
    id: number;
    taskId: number;
    pricingId: number;
    name: string;
    type: number;
    points: number;
    quantity: number;
    subtotal: number;
    createTime: string;
  }

  type EResource = {
    id: number;
    userId: number;
    taskId: number;
    taskResourceId: number;
    location: GeoPoint;
    type: string;
    storeKey: string;
    enabled: number;
    reviewResult: number;
    reviewId: number;
  }

  type EPoint = {
    id: number;
    userId: number;
    type: number;
    points: number;
    relationType: number;
    relationId: number;
    balance: number;
    status: number;
    user: any;
    task: any;
    taskResource: any;
    createTime: string;
  }

  type ETaskAllocation = {
    id: number;
    taskId: number;
    userId: number;
    isRead: number;
    priority: number;
    enableTime: string;
    disableTime: string;
    createTime: string;
  }

  type Project = {
    id: number;
    name: string;
    customerId: number;
    relevantId: number;
    startDatetime: string;
    endDatetime: string;
    location: GeoPoint;
    address: string;
    requirements: string;
    adminId: number;
    agentId: number;
    status: number;
    // delFlag: Int
    createTime: string;
    updateTime: string;
    fullName: string;
    relevant: Project;
    customer: UaCustomerInfo;
    admin: UaUserInfo;
    agent: UaUserInfo;
  };

  type Chat = {
    id: number;
    projectId: number;
    chatType: number;
    fromId: number;
    fromType: number;
    fromLocation: GeoPoint;
    toId: number;
    toType: number;
    messageType: number;
    message: String;
    status: number;
    // delFlag: Int
    createTime: string;
    updateTime: string;
    project: Project;
    from: UaUserInfo;
    to: UaUserInfo;
  };
}
