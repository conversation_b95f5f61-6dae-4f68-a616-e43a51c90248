import { request } from 'umi';

/** 登录接口 POST /server/api/auth/login */
export async function login(body: API.LoginParams, options?: { [key: string]: any }) {
  console.log('login api');
  return request<API.LoginResult>('/server/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** uaAuthorityInfo */
export async function uaAuthorityInfos(
  needPage: boolean = true,
  pageSize: number = 10,
  pageNum: number = 1,
  params?: string,
  options?: { [key: string]: any },
) {
  try {
    // 检查 JWT token
    const token = localStorage.getItem('jwt');
    console.log('JWT Token exists:', !!token);
    if (!token) {
      console.error('No JWT token found. Please login first.');
      return null;
    }

    // 解析 JWT token 检查有效性
    try {
      const parts = token.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(atob(parts[1]));
        console.log('JWT payload:', payload);

        // 检查过期时间
        if (payload.exp) {
          const expDate = new Date(payload.exp * 1000);
          const now = new Date();
          console.log('Token expires at:', expDate.toLocaleString());
          console.log('Current time:', now.toLocaleString());
          console.log('Token expired:', expDate < now);

          if (expDate < now) {
            console.error('JWT token has expired. Please login again.');
            localStorage.removeItem('jwt');
            return null;
          }
        }
      }
    } catch (parseError) {
      console.error('Failed to parse JWT token:', parseError);
    }

    console.log('uaAuthorityInfos', needPage, pageSize, pageNum, params, options);
    const rtn = await request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaAuthorityInfos(
          needPage: ${needPage}
          pageSize: ${pageSize}
          pageNum: ${pageNum}
          params: "${params}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              parentId
              parent {
                id
                name
              }
              name
              uri
              fullName  
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
  console.log(rtn);
  return rtn;

  }
  catch (error) {
    console.error(error);
  }
  return null;
}

export async function uaAuthorityInfo(id: number, options?: { [key: string]: any }) {
  // 检查 JWT token
  const token = localStorage.getItem('jwt');
  if (!token) {
    console.error('No JWT token found. Please login first.');
    return null;
  }

  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaAuthorityInfo(
            id: ${id}
        ) {
          ok
          code
          message
          data {
            id
            parentId
            parent {
              id
              name
            }
            name
            uri
            # groupName
            # status
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function insertUaAuthorityInfo(
  parentId: number,
  name: string,
  uri: string,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        insertUaAuthorityInfo(
          parentId: ${parentId}
          name: "${name}"
          uri: "${uri}"
          type: 2
        ) {
          ok
          code
          message
          data {
            id
            parentId
            parent {
              id
              name
            }
            name
            uri
            # groupName
            # status
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function updateUaAuthorityInfo(
  id: number,
  parentId: number,
  name: string,
  uri: string,
  type: number,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        updateUaAuthorityInfo(
          entity: {
            id: ${id}
            parentId: ${parentId}
            name: "${name}"
            uri: "${uri}"
            type: ${type}
          }
        ) {
          ok
          code
          message
          data {
            id
            parentId
            parent {
              id
              name
            }
            name
            uri
            # groupName
            # status
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function deleteUaAuthorityInfo(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        deleteUaAuthorityInfo(
          id: ${id}
        ) {
          ok
          code
          message
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

/** uaRoleInfo */
export async function uaRoleInfos(
  needPage: boolean = true,
  pageSize: number = 10,
  pageNum: number = 1,
  params?: string,
  options?: { [key: string]: any },
) {
  console.log('uaRoleInfos', needPage, pageSize, pageNum, params, options);
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaRoleInfos(
          needPage: ${needPage}
          pageSize: ${pageSize}
          pageNum: ${pageNum}
          params: "${params}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              code
              name
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function uaRoleInfo(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaRoleInfo(
            id: ${id}
        ) {
          ok
          code
          message
          data {
            id
            code
            name
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function insertUaRoleInfo(
  code: string,
  name: string,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        insertUaRoleInfo(
          code: "${code}"
          name: "${name}"
        ) {
          ok
          code
          message
          data {
            id
            code
            name
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function updateUaRoleInfo(
  id: number,
  code: string,
  name: string,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        updateUaRoleInfo(
          entity: {
            id: ${id}
            code: "${code}"
            name: "${name}"
          }
        ) {
          ok
          code
          message
          data {
            id
            code
            name
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function deleteUaRoleInfo(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        deleteUaRoleInfo(
          id: ${id}
        ) {
          ok
          code
          message
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

/** uaRoleAuthorityRelation */
export async function uaRoleAuthorityRelations(
  needPage: boolean = true,
  pageSize: number = 10,
  pageNum: number = 1,
  params?: string,
  options?: { [key: string]: any },
) {
  console.log('uaRoleAuthorityRelations', needPage, pageSize, pageNum, params, options);
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaRoleAuthorityRelations(
          needPage: ${needPage}
          pageSize: ${pageSize}
          pageNum: ${pageNum}
          params: "${params}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              authorityId
              authority {
                id
                name
                fullName
              }
              fullName
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function uaRoleAuthorityRelation(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaRoleAuthorityRelation(
            id: ${id}
        ) {
          ok
          code
          message
          data {
            id
            authority {
              id
              name
              fullName
            }
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function insertUaRoleAuthorityRelation(
  roleId: number,
  authorityId: number,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        insertUaRoleAuthorityRelation(
          entity: {
            roleId: ${roleId}
            authorityId: ${authorityId}
          }
        ) {
          ok
          code
          message
          data {
            id
            authority {
              id
              name
              fullName
            }
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function updateUaRoleAuthorityRelation(
  id: number,
  roleId: number,
  authorityId: number,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        updateUaRoleAuthorityRelation(
          entity: {
            id: ${id}
            roleId: ${roleId}
            authorityId: ${authorityId}
          }
        ) {
          ok
          code
          message
          data {
            id
            authority {
              id
              name
              fullName
            }
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function deleteUaRoleAuthorityRelation(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        deleteUaRoleAuthorityRelation(
          id: ${id}
        ) {
          ok
          code
          message
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function setUaRoleAuthorityRelations(
  roleId: number,
  authorityIds: string,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        setUaRoleAuthorityRelations(
          roleId: ${roleId}
          authorityIds: "${authorityIds}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              roleId
              role {
                id
                name
              }
              authorityId
              authority {
                id
                name
                fullName
              }
              fullName
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

/** uaUserInfo */
export async function uaUserInfos(
  needPage: boolean = true,
  pageSize: number = 10,
  pageNum: number = 1,
  params?: string,
  options?: { [key: string]: any },
) {
  console.log('uaUserInfos', needPage, pageSize, pageNum, params, options);
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaUserInfos(
          needPage: ${needPage}
          pageSize: ${pageSize}
          pageNum: ${pageNum}
          params: "${params}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              name
              type
              status
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function uaUserInfo(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaUserInfo(
            id: ${id}
        ) {
          ok
          code
          message
          data {
            id
            name
            type
            status
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function insertUaUserInfo(
  name: string,
  password: string,
  type: number,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        insertUaUserInfo(
          name: "${name}"
          password: "${password}"
          type: ${type}
        ) {
          ok
          code
          message
          data {
            id
            name
            type
            status
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function updateUaUserInfo(
  id: number,
  name: string,
  password: string,
  type: number,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        updateUaUserInfo(
          entity: {
            id: ${id}
            name: "${name}"
            password: "${password}"
            type: ${type}
            }
        ) {
          ok
          code
          message
          data {
            id
            name
            type
            status
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function deleteUaUserInfo(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        deleteUaUserInfo(
          id: ${id}
        ) {
          ok
          code
          message
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

// export async function uaUserInfos(options?: { [key: string]: any }) {
//   return request<RestAPI.ApiResult>('/server/api/graphql', {
//     method: 'POST',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     data: JSON.stringify({
//       query: `{
//         uaUserInfos(
//           needPage: true
//           pageSize: 10
//           pageNum: 1
//         ) {
//           ok
//           status
//           message
//           data {
//             paging
//             totalCount
//             pageSize
//             pageNum
//             pageCount
//             list {
//               id
//               name
//               type
//               historyName
//               status
//               phone
//               email
//               remark
//               roles {
//                 id
//                 code
//               }
//               authorities {
//                 id
//                 uri
//                 name
//                 fullName
//               }
//             }
//           }
//         }
//       }`,
//       variables: {},
//     }),
//     ...(options || {}),
//   });
// }

// export async function uaUserInfo(id: number, options?: { [key: string]: any }) {
//   return request<RestAPI.ApiResult>('/server/api/graphql', {
//     method: 'POST',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     data: JSON.stringify({
//       query: `{
//         uaUserInfo(
//             id: ${id}
//         ) {
//           ok
//           status
//           message
//           data {
//             id
//             name
//             type
//             historyName
//             status
//             phone
//             email
//             remark
//             roles {
//               id
//               code
//             }
//             authorities {
//               id
//               uri
//               name
//               fullName
//             }
//           }
//         }
//       }`,
//       variables: {},
//     }),
//     ...(options || {}),
//   });
// }

/** uaUserRoleRelation */
export async function uaUserRoleRelations(
  needPage: boolean = true,
  pageSize: number = 10,
  pageNum: number = 1,
  params?: string,
  options?: { [key: string]: any },
) {
  console.log('uaUserRoleRelations', needPage, pageSize, pageNum, params, options);
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaUserRoleRelations(
          needPage: ${needPage}
          pageSize: ${pageSize}
          pageNum: ${pageNum}
          params: "${params}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              roleId
              role {
                id
                name
              }
              fullName
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function uaUserRoleRelation(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaUserRoleRelation(
            id: ${id}
        ) {
          ok
          code
          message
          data {
            id
            roleId
            role {
              id
              name
            }
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function insertUaUserRoleRelation(
  userId: number,
  roleId: number,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        insertUaUserRoleRelation(
          entity: {
            userId: ${userId}
            roleId: ${roleId}
          }
        ) {
          ok
          code
          message
          data {
            id
            roleId
            role {
              id
              name
            }
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function updateUaUserRoleRelation(
  id: number,
  userId: number,
  roleId: number,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        updateUaUserRoleRelation(
          entity: {
            id: ${id}
            userId: ${userId}
            roleId: ${roleId}
          }
        ) {
          ok
          code
          message
          data {
            id
            roleId
            role {
              id
              name
            }
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function deleteUaUserRoleRelation(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        deleteUaUserRoleRelation(
          id: ${id}
        ) {
          ok
          code
          message
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function setUaUserRoleRelations(
  userId: number,
  roleIds: string,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        setUaUserRoleRelations(
          userId: ${userId}
          roleIds: "${roleIds}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              userId
              user {
                id
                name
              }
              roleId
              role {
                id
                name
              }
              fullName
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

/** uaCustomerInfo */
export async function uaCustomerInfos(
  needPage: boolean = true,
  pageSize: number = 10,
  pageNum: number = 1,
  params?: string,
  options?: { [key: string]: any },
) {
  console.log('uaCustomerInfos', needPage, pageSize, pageNum, params, options);
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaCustomerInfos(
          needPage: ${needPage}
          pageSize: ${pageSize}
          pageNum: ${pageNum}
          params: "${params}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              name
              contacts
              phone
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function uaCustomerInfo(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaCustomerInfo(
            id: ${id}
        ) {
          ok
          code
          message
          data {
            id
            name
            contacts
            phone
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function insertUaCustomerInfo(
  name: string,
  contacts: string,
  phone: string,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        insertUaUserInfo(
          name: "${name}"
          contacts: "${contacts}"
          phone: "${phone}"
        ) {
          ok
          code
          message
          data {
            id
            name
            contacts
            phone
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function updateUaCustomerInfo(
  id: number,
  name: string,
  contacts: string,
  phone: string,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        updateUaCustomerInfo(
          entity: {
            id: ${id}
            name: "${name}"
            contacts: "${contacts}"
            phone: "${phone}"
          }
        ) {
          ok
          code
          message
          data {
            id
            name
            contacts
            phone
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function deleteUaCustomerInfo(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        deleteUaCustomerInfo(
          id: ${id}
        ) {
          ok
          code
          message
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

/** uaCustomerUserRelation */
export async function uaCustomerUserRelations(
  needPage: boolean = true,
  pageSize: number = 10,
  pageNum: number = 1,
  params?: string,
  options?: { [key: string]: any },
) {
  console.log('uaCustomerUserRelations', needPage, pageSize, pageNum, params, options);
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaCustomerUserRelations(
          needPage: ${needPage}
          pageSize: ${pageSize}
          pageNum: ${pageNum}
          params: "${params}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              userId
              user {
                id
                name
              }
              customerId
              customer {
                id
                name
              }
              fullName
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function uaCustomerUserRelation(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `{
        uaCustomerUserRelation(
            id: ${id}
        ) {
          ok
          code
          message
          data {
            id
            userId
            user {
              id
              name
            }
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function insertUaCustomerUserRelation(
  customerId: number,
  userId: number,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        insertUaCustomerUserRelation(
          entity: {
            customerId: ${customerId}
            userId: ${userId}
          }
        ) {
          ok
          code
          message
          data {
            id
            userId
            user {
              id
              name
            }
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function updateUaCustomerUserRelation(
  id: number,
  customerId: number,
  userId: number,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        updateUaCustomerUserRelation(
          entity: {
            id: ${id}
            customerId: ${customerId}
            userId: ${userId}
          }
        ) {
          ok
          code
          message
          data {
            id
            userId
            user {
              id
              name
            }
            fullName
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function deleteUaCustomerUserRelation(id: number, options?: { [key: string]: any }) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        deleteUaCustomerUserRelation(
          id: ${id}
        ) {
          ok
          code
          message
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}

export async function setUaCustomerUserRelations(
  customerId: number,
  userIds: string,
  options?: { [key: string]: any },
) {
  return request<RestAPI.ApiResult>('/server/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      query: `mutation {
        setUaCustomerUserRelations(
          customerId: ${customerId}
          userIds: "${userIds}"
        ) {
          ok
          code
          message
          data {
            paging
            totalCount
            pageSize
            pageNum
            pageCount
            list {
              id
              customerId
              customer {
                id
                name
              }
              userId
              user {
                id
                name
              }
              fullName
            }
          }
        }
      }`,
      variables: {},
    }),
    ...(options || {}),
  });
}
