export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './user/Login',
      },
      {
        component: './404',
      },
    ],
  },
  {
    path: '/welcome',
    name: 'welcome',
    icon: 'smile',
    component: './Welcome',
  },

  // {
  //   path: '/project',
  //   name: 'project',
  //   icon: 'crown',
  //   access: 'hasRole',
  //   requestRole: 'admin',
  //   component: './project',
  // },
  // // {
  // //   path: '/demo',
  // //   name: 'demo',
  // //   icon: 'crown',
  // //   access: 'hasRole',
  // //   requestRole: 'admin',
  // //   component: './demo',
  // // },
  // {
  //   path: '/customer',
  //   name: 'customer',
  //   icon: 'crown',
  //   access: 'hasRole',
  //   requestRole: 'user',
  //   component: './customer',
  // },
  // {
  //   path: '/agent',
  //   name: 'agent',
  //   icon: 'crown',
  //   access: 'hasRole',
  //   requestRole: 'agent',
  //   component: './agent',
  // },
  // // {
  // //   name: 'list.table-list',
  // //   icon: 'table',
  // //   path: '/list',
  // //   component: './TableList',
  // // },
  // {
  //   path: '/e',
  //   name: 'e',
  //   icon: 'crown',
  //   access: 'hasRole',
  //   requestRole: 'admin',
  //   routes: [
  //     {
  //       path: '/e/review',
  //       name: 'review',
  //       icon: 'smile',
  //       access: 'hasAuthority',
  //       requestAuthority: 'e-review',
  //       // component: './e/review',
  //       routes: [
  //         {
  //           path: '/e/review/etask',
  //           name: 'etask',
  //           // icon: 'smile',
  //           access: 'hasAuthority',
  //           requestAuthority: 'e-review',
  //           component: './e/review/etask',
  //         },
  //         {
  //           path: '/e/review/eresource',
  //           name: 'eresource',
  //           // icon: 'smile',
  //           access: 'hasAuthority',
  //           requestAuthority: 'e-review',
  //           component: './e/review/eresource',
  //         },
  //       ]
  //     },
  //     {
  //       path: '/e/categorize',
  //       name: 'categorize',
  //       icon: 'smile',
  //       access: 'hasAuthority',
  //       requestAuthority: 'e-categorize',
  //       component: './e/categorize',
  //     },
  //     {
  //       path: '/e/pricing',
  //       name: 'pricing',
  //       icon: 'smile',
  //       access: 'hasAuthority',
  //       requestAuthority: 'e-pricing',
  //       component: './e/pricing',
  //     },
  //     {
  //       path: '/e/taskTemplate',
  //       name: 'taskTemplate',
  //       icon: 'smile',
  //       access: 'hasAuthority',
  //       requestAuthority: 'e-task-template',
  //       component: './e/taskTemplate',
  //     },
  //     {
  //       path: '/e/task',
  //       name: 'task',
  //       icon: 'smile',
  //       access: 'hasAuthority',
  //       requestAuthority: 'e-task',
  //       component: './e/task',
  //     },
  //     {
  //       path: '/e/customerService',
  //       name: 'customerService',
  //       icon: 'smile',
  //       access: 'hasAuthority',
  //       requestAuthority: 'e-customer-service',
  //       // component: './e/customerservice',
  //       routes: [
  //         {
  //           path: '/e/customerService/qa',
  //           name: 'qa',
  //           // icon: 'smile',
  //           access: 'hasAuthority',
  //           requestAuthority: 'e-customer-service',
  //           component: './e/customerService/qa',
  //         },
  //         {
  //           path: '/e/customerService/online',
  //           name: 'online',
  //           // icon: 'smile',
  //           access: 'hasAuthority',
  //           requestAuthority: 'e-customer-service',
  //           component: './e/customerService/online',
  //         },
  //       ]
  //     },
  //     {
  //       component: './404',
  //     },
  //   ],
  // },
  {
    path: '/admin',
    name: 'admin',
    icon: 'crown',
    access: 'hasRole',
    requestRole: 'admin',
    routes: [
      {
        path: '/admin/authority',
        name: 'authority',
        icon: 'smile',
        access: 'hasAuthority',
        requestAuthority: 'ua-authority-info',
        component: './admin/authorityInfo',
      },
      {
        path: '/admin/role',
        name: 'role',
        icon: 'smile',
        access: 'hasAuthority',
        requestAuthority: 'ua-role-info',
        component: './admin/roleInfo',
      },
      {
        path: '/admin/user',
        name: 'user',
        icon: 'smile',
        access: 'hasAuthority',
        requestAuthority: 'ua-user-info',
        component: './admin/userInfo',
      },
      // {
      //   path: '/admin/customer',
      //   name: 'customer',
      //   icon: 'smile',
      //   access: 'hasAuthority',
      //   requestAuthority: 'ua-customer-info',
      //   component: './admin/customerInfo',
      // },
      {
        component: './404',
      },
    ],
  },
  {
    path: '/',
    redirect: '/welcome',
  },
  {
    component: './404',
  },
];
