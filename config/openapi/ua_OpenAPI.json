{"swagger": "2.0", "info": {"description": "shiro-boot-session api", "version": "1.0", "title": "ua模块 api文档"}, "host": "localhost:8881", "basePath": "/", "tags": [{"name": "权限信息", "description": "Ua Authority Info Controller"}, {"name": "用户信息", "description": "Ua User Info Controller"}, {"name": "用户角色关联关系", "description": "Ua User Role Relation Controller"}, {"name": "角色信息", "description": "Ua Role Info Controller"}, {"name": "角色权限关联关系", "description": "Ua Role Authority Relation Controller"}, {"name": "认证", "description": "Login Controller"}], "paths": {"/auth/clearCachedAuthorization": {"post": {"tags": ["认证"], "summary": "清除授权缓存", "operationId": "clearCachedAuthorizationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "username", "in": "query", "description": "username", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«Void»", "$ref": "#/definitions/ApiResult«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/auth/login": {"post": {"tags": ["认证"], "summary": "登录", "operationId": "loginUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"originalRef": "LoginRegistryParam", "$ref": "#/definitions/LoginRegistryParam"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«AccessToken»", "$ref": "#/definitions/ApiResult«AccessToken»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/auth/logout": {"post": {"tags": ["认证"], "summary": "退出", "operationId": "logoutUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«Void»", "$ref": "#/definitions/ApiResult«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/auth/registry": {"post": {"tags": ["认证"], "summary": "注册", "operationId": "registryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"originalRef": "LoginRegistryParam", "$ref": "#/definitions/LoginRegistryParam"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaUserInfo»", "$ref": "#/definitions/ApiResult«UaUserInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/ua-authority-info": {"get": {"tags": ["权限信息"], "summary": "列表查询", "operationId": "queryListUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "needPage", "in": "query", "description": "needPage", "required": false, "type": "boolean", "default": false}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "type": "integer", "default": 1, "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "type": "integer", "default": 10, "format": "int32"}, {"name": "params", "in": "query", "description": "params", "required": false, "items": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«PageResp4UaAuthorityInfo»", "$ref": "#/definitions/ApiResult«PageResp4UaAuthorityInfo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "post": {"tags": ["权限信息"], "summary": "新增", "operationId": "insertUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaAuthorityInfo", "$ref": "#/definitions/UaAuthorityInfo"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaAuthorityInfo»", "$ref": "#/definitions/ApiResult«UaAuthorityInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "put": {"tags": ["权限信息"], "summary": "通过主键更新", "operationId": "updateUsingPUT", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaAuthorityInfo", "$ref": "#/definitions/UaAuthorityInfo"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaAuthorityInfo»", "$ref": "#/definitions/ApiResult«UaAuthorityInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/ua-authority-info/{id}": {"get": {"tags": ["权限信息"], "summary": "通过主键查询", "operationId": "getOneUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaAuthorityInfo»", "$ref": "#/definitions/ApiResult«UaAuthorityInfo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "delete": {"tags": ["权限信息"], "summary": "通过主键删除", "operationId": "deleteUsingDELETE", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«Void»", "$ref": "#/definitions/ApiResult«Void»"}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}}}, "/ua-role-authority-relation": {"get": {"tags": ["角色权限关联关系"], "summary": "列表查询", "operationId": "queryListUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "needPage", "in": "query", "description": "needPage", "required": false, "type": "boolean", "default": false}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "type": "integer", "default": 1, "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "type": "integer", "default": 10, "format": "int32"}, {"name": "params", "in": "query", "description": "params", "required": false, "items": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«PageResp«UaRoleAuthorityRelation»»", "$ref": "#/definitions/ApiResult«PageResp«UaRoleAuthorityRelation»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "post": {"tags": ["角色权限关联关系"], "summary": "新增", "operationId": "insertUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaRoleAuthorityRelation", "$ref": "#/definitions/UaRoleAuthorityRelation"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaRoleAuthorityRelation»", "$ref": "#/definitions/ApiResult«UaRoleAuthorityRelation»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "put": {"tags": ["角色权限关联关系"], "summary": "通过主键更新", "operationId": "updateUsingPUT_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaRoleAuthorityRelation", "$ref": "#/definitions/UaRoleAuthorityRelation"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaRoleAuthorityRelation»", "$ref": "#/definitions/ApiResult«UaRoleAuthorityRelation»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/ua-role-authority-relation/{id}": {"get": {"tags": ["角色权限关联关系"], "summary": "通过主键查询", "operationId": "getOneUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaRoleAuthorityRelation»", "$ref": "#/definitions/ApiResult«UaRoleAuthorityRelation»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "delete": {"tags": ["角色权限关联关系"], "summary": "通过主键删除", "operationId": "deleteUsingDELETE_1", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«Void»", "$ref": "#/definitions/ApiResult«Void»"}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}}}, "/ua-role-info": {"get": {"tags": ["角色信息"], "summary": "列表查询", "operationId": "queryListUsingGET_2", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "needPage", "in": "query", "description": "needPage", "required": false, "type": "boolean", "default": false}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "type": "integer", "default": 1, "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "type": "integer", "default": 10, "format": "int32"}, {"name": "params", "in": "query", "description": "params", "required": false, "items": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«PageResp«UaRoleInfo»»", "$ref": "#/definitions/ApiResult«PageResp«UaRoleInfo»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "post": {"tags": ["角色信息"], "summary": "新增", "operationId": "insertUsingPOST_2", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaRoleInfo", "$ref": "#/definitions/UaRoleInfo"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaRoleInfo»", "$ref": "#/definitions/ApiResult«UaRoleInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "put": {"tags": ["角色信息"], "summary": "通过主键更新", "operationId": "updateUsingPUT_2", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaRoleInfo", "$ref": "#/definitions/UaRoleInfo"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaRoleInfo»", "$ref": "#/definitions/ApiResult«UaRoleInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/ua-role-info/{id}": {"get": {"tags": ["角色信息"], "summary": "通过主键查询", "operationId": "getOneUsingGET_2", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaRoleInfo»", "$ref": "#/definitions/ApiResult«UaRoleInfo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "delete": {"tags": ["角色信息"], "summary": "通过主键删除", "operationId": "deleteUsingDELETE_2", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«Void»", "$ref": "#/definitions/ApiResult«Void»"}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}}}, "/ua-user-info": {"get": {"tags": ["用户信息"], "summary": "列表查询", "operationId": "queryListUsingGET_3", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "needPage", "in": "query", "description": "needPage", "required": false, "type": "boolean", "default": false}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "type": "integer", "default": 1, "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "type": "integer", "default": 10, "format": "int32"}, {"name": "params", "in": "query", "description": "params", "required": false, "items": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«PageResp«UaUserInfo»»", "$ref": "#/definitions/ApiResult«PageResp«UaUserInfo»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "post": {"tags": ["用户信息"], "summary": "新增", "operationId": "insertUsingPOST_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaUserInfo", "$ref": "#/definitions/UaUserInfo"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaUserInfo»", "$ref": "#/definitions/ApiResult«UaUserInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "put": {"tags": ["用户信息"], "summary": "通过主键更新", "operationId": "updateUsingPUT_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaUserInfo", "$ref": "#/definitions/UaUserInfo"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaUserInfo»", "$ref": "#/definitions/ApiResult«UaUserInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/ua-user-info/{id}": {"get": {"tags": ["用户信息"], "summary": "通过主键查询", "operationId": "getOneUsingGET_3", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaUserInfo»", "$ref": "#/definitions/ApiResult«UaUserInfo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "delete": {"tags": ["用户信息"], "summary": "通过主键删除", "operationId": "deleteUsingDELETE_3", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«Void»", "$ref": "#/definitions/ApiResult«Void»"}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}}}, "/ua-user-role-relation": {"get": {"tags": ["用户角色关联关系"], "summary": "列表查询", "operationId": "queryListUsingGET_4", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "needPage", "in": "query", "description": "needPage", "required": false, "type": "boolean", "default": false}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "type": "integer", "default": 1, "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "type": "integer", "default": 10, "format": "int32"}, {"name": "params", "in": "query", "description": "params", "required": false, "items": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«PageResp«UaUserRoleRelation»»", "$ref": "#/definitions/ApiResult«PageResp«UaUserRoleRelation»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "post": {"tags": ["用户角色关联关系"], "summary": "新增", "operationId": "insertUsingPOST_4", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaUserRoleRelation", "$ref": "#/definitions/UaUserRoleRelation"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaUserRoleRelation»", "$ref": "#/definitions/ApiResult«UaUserRoleRelation»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "put": {"tags": ["用户角色关联关系"], "summary": "通过主键更新", "operationId": "updateUsingPUT_4", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"originalRef": "UaUserRoleRelation", "$ref": "#/definitions/UaUserRoleRelation"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaUserRoleRelation»", "$ref": "#/definitions/ApiResult«UaUserRoleRelation»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/ua-user-role-relation/{id}": {"get": {"tags": ["用户角色关联关系"], "summary": "通过主键查询", "operationId": "getOneUsingGET_4", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«UaUserRoleRelation»", "$ref": "#/definitions/ApiResult«UaUserRoleRelation»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "delete": {"tags": ["用户角色关联关系"], "summary": "通过主键删除", "operationId": "deleteUsingDELETE_4", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "required": true, "type": "string", "default": "Bearer "}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ApiResult«Void»", "$ref": "#/definitions/ApiResult«Void»"}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}}}}, "definitions": {"AccessToken": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "refType": null}, "loginTime": {"type": "string", "format": "date-time", "refType": null}, "permissions": {"type": "array", "uniqueItems": true, "items": {"type": "string"}, "refType": "string"}, "roles": {"type": "array", "uniqueItems": true, "items": {"type": "string"}, "refType": "string"}, "token": {"type": "string", "refType": null}, "username": {"type": "string", "refType": null}}, "title": "AccessToken"}, "ApiResult«AccessToken»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "AccessToken", "$ref": "#/definitions/AccessToken"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«AccessToken»"}, "ApiResult«PageResp4UaAuthorityInfo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "PageResp4UaAuthorityInfo", "$ref": "#/definitions/PageResp4UaAuthorityInfo"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«PageResp4UaAuthorityInfo»"}, "ApiResult«PageResp«UaRoleAuthorityRelation»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "PageResp«UaRoleAuthorityRelation»", "$ref": "#/definitions/PageResp«UaRoleAuthorityRelation»"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«PageResp«UaRoleAuthorityRelation»»"}, "ApiResult«PageResp«UaRoleInfo»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "PageResp«UaRoleInfo»", "$ref": "#/definitions/PageResp«UaRoleInfo»"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«PageResp«UaRoleInfo»»"}, "ApiResult«PageResp«UaUserInfo»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "PageResp«UaUserInfo»", "$ref": "#/definitions/PageResp«UaUserInfo»"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«PageResp«UaUserInfo»»"}, "ApiResult«PageResp«UaUserRoleRelation»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "PageResp«UaUserRoleRelation»", "$ref": "#/definitions/PageResp«UaUserRoleRelation»"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«PageResp«UaUserRoleRelation»»"}, "ApiResult«UaAuthorityInfo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "UaAuthorityInfo", "$ref": "#/definitions/UaAuthorityInfo"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«UaAuthorityInfo»"}, "ApiResult«UaRoleAuthorityRelation»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "UaRoleAuthorityRelation", "$ref": "#/definitions/UaRoleAuthorityRelation"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«UaRoleAuthorityRelation»"}, "ApiResult«UaRoleInfo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "UaRoleInfo", "$ref": "#/definitions/UaRoleInfo"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«UaRoleInfo»"}, "ApiResult«UaUserInfo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "UaUserInfo", "$ref": "#/definitions/UaUserInfo"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«UaUserInfo»"}, "ApiResult«UaUserRoleRelation»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"originalRef": "UaUserRoleRelation", "$ref": "#/definitions/UaUserRoleRelation"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«UaUserRoleRelation»"}, "ApiResult«Void»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "ok": {"type": "boolean"}}, "title": "ApiResult«Void»"}, "LoginRegistryParam": {"type": "object", "required": ["password", "username"], "properties": {"password": {"type": "string"}, "username": {"type": "string"}}, "title": "LoginRegistryParam"}, "PageResp4UaAuthorityInfo": {"type": "object", "properties": {"list": {"type": "array", "items": {"originalRef": "UaAuthorityInfo", "$ref": "#/definitions/UaAuthorityInfo"}}, "pageCount": {"type": "integer", "format": "int64"}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "paging": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int64"}}, "title": "PageResp4UaAuthorityInfo"}, "PageResp«UaRoleAuthorityRelation»": {"type": "object", "properties": {"list": {"type": "array", "items": {"originalRef": "UaRoleAuthorityRelation", "$ref": "#/definitions/UaRoleAuthorityRelation"}}, "pageCount": {"type": "integer", "format": "int64"}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "paging": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int64"}}, "title": "PageResp«UaRoleAuthorityRelation»"}, "PageResp«UaRoleInfo»": {"type": "object", "properties": {"list": {"type": "array", "items": {"originalRef": "UaRoleInfo", "$ref": "#/definitions/UaRoleInfo"}}, "pageCount": {"type": "integer", "format": "int64"}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "paging": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int64"}}, "title": "PageResp«UaRoleInfo»"}, "PageResp«UaUserInfo»": {"type": "object", "properties": {"list": {"type": "array", "items": {"originalRef": "UaUserInfo", "$ref": "#/definitions/UaUserInfo"}}, "pageCount": {"type": "integer", "format": "int64"}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "paging": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int64"}}, "title": "PageResp«UaUserInfo»"}, "PageResp«UaUserRoleRelation»": {"type": "object", "properties": {"list": {"type": "array", "items": {"originalRef": "UaUserRoleRelation", "$ref": "#/definitions/UaUserRoleRelation"}}, "pageCount": {"type": "integer", "format": "int64"}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "paging": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int64"}}, "title": "PageResp«UaUserRoleRelation»"}, "UaAuthorityInfo": {"type": "object", "properties": {"createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "delFlag": {"type": "integer", "format": "int32", "description": "是否删除标识"}, "groupName": {"type": "string", "description": "分组"}, "hide": {"type": "boolean", "description": "掩藏"}, "icon": {"type": "string", "description": "图标"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "description": "权限名称"}, "parentId": {"type": "integer", "format": "int64", "description": "上级id"}, "permTag": {"type": "string", "description": "权限标识"}, "remark": {"type": "string", "description": "备注"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "integer", "format": "int32", "description": "状态"}, "type": {"type": "integer", "format": "int32", "description": "类型[1-菜单，2-按钮/api]"}, "updateTime": {"type": "string", "format": "date-time", "description": "变更时间"}, "uri": {"type": "string", "description": "URI"}, "view": {"type": "string", "description": "视图"}}, "title": "UaAuthorityInfo", "description": "权限信息表实体类"}, "UaRoleAuthorityRelation": {"type": "object", "properties": {"authorityId": {"type": "integer", "format": "int64", "description": "权限id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "id": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64", "description": "角色id"}}, "title": "UaRoleAuthorityRelation", "description": "角色权限关联关系表实体类"}, "UaRoleInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "角色编号"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "delFlag": {"type": "integer", "format": "int32", "description": "是否删除标识"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "description": "角色名称"}, "remark": {"type": "string", "description": "备注"}, "status": {"type": "integer", "format": "int32", "description": "角色状态[1-正常，2-禁用]"}, "updateTime": {"type": "string", "format": "date-time", "description": "变更时间"}}, "title": "UaRoleInfo", "description": "角色信息表实体类"}, "UaUserInfo": {"type": "object", "properties": {"createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "delFlag": {"type": "integer", "format": "int32", "description": "是否删除标识"}, "email": {"type": "string", "description": "邮箱"}, "historyName": {"type": "string", "description": "历史名称"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}, "phone": {"type": "string", "description": "电话"}, "remark": {"type": "string", "description": "备注"}, "status": {"type": "integer", "format": "int32", "description": "用户状态[1-正常，2-锁定]"}, "updateTime": {"type": "string", "format": "date-time", "description": "变更时间"}}, "title": "UaUserInfo", "description": "用户信息表实体类"}, "UaUserRoleRelation": {"type": "object", "properties": {"createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "id": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64", "description": "角色id"}, "userId": {"type": "integer", "format": "int64", "description": "用户id"}}, "title": "UaUserRoleRelation", "description": "用户角色关联关系表实体类"}}}