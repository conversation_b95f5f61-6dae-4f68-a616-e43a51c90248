/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  local: {
    '/server/api/': {
      target: 'http://localhost:8881/',
      changeOrigin: true,
      pathRewrite: { '^/server/api': '' },
      onProxyReq: (proxyReq: any) => {
        // 添加 CORS 头部处理
        proxyReq.setHeader('Origin', 'http://localhost:8881');
      },
      onProxyRes: (proxyRes: any) => {
        // 添加 CORS 响应头
        proxyRes.headers['Access-Control-Allow-Origin'] = 'http://localhost:5081';
        proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
        proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization';
        proxyRes.headers['Access-Control-Allow-Credentials'] = 'true';
      },
    },
    '/server/tmap/': {
      target: 'https://apis.map.qq.com',
      changeOrigin: true,
      pathRewrite: { '^/server/tmap': '' },
    },

    // '/server/api/graphql': {
    //   target: 'http://localhost:8880/',
    //   changeOrigin: true,
    //   pathRewrite: { '^/server/api': '' },
    // },
  },
  dev: {
    '/server/api/': {
      target: 'http://localhost:8082/',
      changeOrigin: true,
      pathRewrite: { '^/server': '' },
      // // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
      // '/server/api/': {
      //   // 要代理的地址
      //   target: 'https://preview.pro.ant.design',
      //   // 配置了这个可以从 http 代理到 https
      //   // 依赖 origin 的功能可能需要这个，比如 cookie
      //   changeOrigin: true,
      //   pathRewrite: { '^/server': '' },
    },
    '/server/tmap/': {
      target: 'https://apis.map.qq.com',
      changeOrigin: true,
      pathRewrite: { '^/server/tmap': '' },
    },
    // '/server/api/graphql': {
    //   target: 'http://localhost:8880/',
    //   changeOrigin: true,
    //   pathRewrite: { '^/server/api': '' },
    // },
  },
  test: {
    '/server/api/': {
      target: 'https://proapi.azurewebsites.net',
      changeOrigin: true,
      pathRewrite: { '^/server': '' },
    },
  },
  pre: {
    '/server/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^/server': '' },
    },
  },
};
