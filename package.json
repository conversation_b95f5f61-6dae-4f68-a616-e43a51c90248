{"name": "ant-design-pro", "version": "5.2.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "playwright": "playwright install && playwright test", "prepare": "husky install", "prettier": "prettier -c --write \"src/**/*\"", "serve": "umi-serve", "start": "cross-env NODE_OPTIONS=--openssl-legacy-provider UMI_ENV=dev PORT=5081 umi dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev umi dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev umi dev", "start:no-ui": "cross-env UMI_UI=none UMI_ENV=dev umi dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev umi dev", "test": "umi test", "test:component": "umi test ./src/components", "test:e2e": "node ./tests/run-tests.js", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.7.0", "@ant-design/pro-card": "^1.19.0", "@ant-design/pro-components": "1.1.1", "@ant-design/pro-descriptions": "^1.10.0", "@ant-design/pro-form": "^1.64.0", "@ant-design/pro-layout": "^6.35.0", "@ant-design/pro-table": "^2.71.0", "@antv/data-set": "^0.11.0", "@antv/l7": "^2.3.7", "@antv/l7-maps": "^2.3.7", "@antv/l7-react": "^2.1.9", "@map-component/react-tmap": "^0.1.4", "@umijs/route-utils": "^2.0.0", "ahooks": "^2.0.0", "antd": "^4.19.0", "bizcharts": "^3.5.3-beta.0", "bizcharts-plugin-slider": "^2.1.1-beta.1", "classnames": "^2.3.0", "dayjs": "^1.11.10", "gg-editor": "^2.0.2", "lodash": "^4.17.0", "lodash-decorators": "^6.0.0", "moment": "^2.29.0", "numeral": "^2.0.6", "nzh": "^1.0.3", "omit.js": "^2.0.2", "rc-menu": "^9.1.0", "rc-util": "^5.16.0", "react": "^17.0.0", "react-dev-inspector": "^1.7.0", "react-dom": "^17.0.0", "react-fittext": "^1.0.0", "react-helmet-async": "^1.2.0", "react-infinite-scroll-component": "^6.1.0", "react-router": "^4.3.1", "ts-md5": "^1.3.1", "umi": "^3.5.0", "umi-serve": "^1.9.10"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.0", "@playwright/test": "^1.17.0", "@types/express": "^4.17.0", "@types/history": "^4.7.0", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.0", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@umijs/fabric": "^2.8.0", "@umijs/openapi": "^1.3.0", "@umijs/plugin-blocks": "^2.2.0", "@umijs/plugin-esbuild": "^1.4.0", "@umijs/plugin-openapi": "^1.3.0", "@umijs/preset-ant-design-pro": "^1.3.0", "@umijs/preset-dumi": "^1.1.0", "@umijs/preset-react": "^2.1.0", "cross-env": "^7.0.0", "cross-port-killer": "^1.3.0", "detect-installer": "^1.0.0", "eslint": "^7.32.0", "gh-pages": "^3.2.0", "husky": "^7.0.4", "jsdom-global": "^3.0.0", "lint-staged": "^10.0.0", "mockjs": "^1.1.0", "prettier": "^2.5.0", "stylelint": "^13.0.0", "swagger-ui-react": "^3.52.0", "typescript": "^4.5.0", "umi-serve": "^1.9.10"}, "engines": {"node": ">=12.0.0"}}