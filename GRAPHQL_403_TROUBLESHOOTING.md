# GraphQL 403 Forbidden 错误解决指南

## 问题描述
当访问 GraphQL API (`/server/api/graphql`) 时出现 403 Forbidden 错误，错误信息显示 "无permissions"。

## 问题原因
这是一个**认证和权限**问题，主要原因包括：

1. **未登录** - 没有有效的 JWT token
2. **Token 过期** - JWT token 已过期
3. **权限不足** - 用户没有访问特定 GraphQL 查询的权限

## 解决步骤

### 1. 检查登录状态

#### 方法一：使用调试工具（推荐）
1. 打开浏览器开发者工具（F12）
2. 在 Console 中运行：
   ```javascript
   // 检查认证状态
   AuthDebug.fullCheck()
   
   // 或者分步检查
   AuthDebug.checkToken()           // 检查 JWT token
   AuthDebug.testGraphQLConnection() // 测试 GraphQL 连接
   AuthDebug.testAuthenticatedRequest() // 测试认证请求
   ```

#### 方法二：手动检查
1. 在 Console 中运行：
   ```javascript
   console.log('JWT Token:', localStorage.getItem('jwt'));
   ```
2. 如果返回 `null`，说明未登录

### 2. 重新登录

如果没有有效的 JWT token：

1. **访问登录页面**：http://localhost:5081/user/login
2. **输入有效的用户名和密码**
3. **登录成功后会自动存储 JWT token**

### 3. 验证权限

登录后，确保您的用户账户具有以下权限：
- `ua-authority-info` - 访问权限信息
- `ua-role-info` - 访问角色信息
- `ua-user-info` - 访问用户信息

### 4. 清除缓存（如果需要）

如果仍有问题，尝试清除认证缓存：
```javascript
// 在浏览器 Console 中运行
AuthDebug.clearAuth()
// 然后重新登录
```

## 技术细节

### 认证流程
1. 用户登录 → 获取 JWT token
2. Token 存储在 `localStorage.jwt`
3. 每个 API 请求自动添加 `Authorization: Bearer <token>` 头
4. 后端验证 token 和权限

### 代理配置
- 前端开发服务器：`localhost:5081`
- 后端 GraphQL 服务：`localhost:8881`
- 代理路径：`/server/api/` → `http://localhost:8881/`

### 常见错误信息
- `"无permissions"` - 权限不足，需要检查用户权限
- `401 Unauthorized` - 未认证，需要登录
- `403 Forbidden` - 已认证但权限不足

## 调试命令

在浏览器 Console 中可用的调试命令：

```javascript
// 完整检查
AuthDebug.fullCheck()

// 检查 token 状态
AuthDebug.checkToken()

// 测试 GraphQL 连接
AuthDebug.testGraphQLConnection()

// 测试认证请求
AuthDebug.testAuthenticatedRequest()

// 清除认证信息
AuthDebug.clearAuth()
```

## 预防措施

1. **定期检查 token 有效性**
2. **实现自动刷新机制**（如果后端支持）
3. **在 token 过期时自动跳转到登录页**

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. 浏览器 Console 中 `AuthDebug.fullCheck()` 的完整输出
2. Network 标签页中失败请求的详细信息
3. 使用的用户账户和权限信息
